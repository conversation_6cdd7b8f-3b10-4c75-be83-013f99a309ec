# WebSocket Payload注入快速参考

## 🚀 快速开始

### 基本模式

```typescript
// ✅ 推荐：嵌套结构
@MessagePattern('service.action')
async someAction(@Payload() payload: { 
  originalDto: SomeDto;
  // 注入字段自动添加到payload根级别
}) {
  const dto = payload.originalDto;     // 纯净的DTO
  const userId = payload.userId;       // 注入的用户ID
  const serverContext = payload.serverContext; // 注入的区服上下文
}

// ✅ 推荐：明确接口
interface EnhancedPayload extends SomeDto {
  userId: string;
  wsContext: { timestamp: number; routingStrategy: string; };
  serverContext?: { serverId: string; characterId: string; };
}

@MessagePattern('service.action')
async someAction(@Payload() payload: EnhancedPayload) {
  // 类型安全，智能提示
}
```

## 📋 注入字段速查

| 字段 | 类型 | 说明 | 可用性 |
|------|------|------|--------|
| `userId` | string | 用户ID | 总是可用 |
| `wsContext.timestamp` | number | 消息时间戳 | 总是可用 |
| `wsContext.routingStrategy` | string | 路由策略 | 总是可用 |
| `wsContext.messageId` | string | 消息ID | 总是可用 |
| `serverContext.serverId` | string | 区服ID | 角色Token时 |
| `serverContext.characterId` | string | 角色ID | 角色Token时 |
| `serverContext.characterLevel` | number | 角色等级 | 角色Token时 |
| `crossServerContext` | object | 跨服上下文 | 跨服路由时 |
| `globalContext` | object | 全服上下文 | 全服路由时 |

## 🗄️ 缓存装饰器速查

### 基础用法

```typescript
@Cacheable({
  key: 'service:action:#{payload.id}',
  serverId: '#{payload.serverId}',
  ttl: 3600
})
```

### 利用注入字段

```typescript
@Cacheable({
  // 包含用户上下文
  key: 'service:action:#{payload.id}:#{payload.userId}',
  // 使用注入的区服上下文
  serverId: '#{payload.serverContext.serverId}',
  // 条件缓存
  condition: '#{payload.serverContext.characterLevel > 10}',
  ttl: 3600
})
```

### 常用表达式

| 表达式 | 说明 |
|--------|------|
| `#{payload.field}` | 访问payload字段 |
| `#{payload.serverContext.serverId}` | 访问嵌套字段 |
| `#{payload.field != null}` | 非空检查 |
| `#{payload.level > 10}` | 数值比较 |
| `#{payload.name == "admin"}` | 字符串比较 |

## ⚠️ 常见错误

### ❌ 错误做法

```typescript
// 1. 直接使用DTO类型
async action(@Payload() dto: SomeDto) {
  const userId = dto.userId; // 类型错误
}

// 2. 多个@Payload装饰器
async action(
  @Payload() dto: SomeDto,
  @Payload('userId') userId: string // 不需要
) {}

// 3. 缓存键缺少payload前缀
@Cacheable({
  key: 'service:#{id}', // 错误
  serverId: '#{serverId}' // 错误
})
```

### ✅ 正确做法

```typescript
// 1. 使用嵌套结构或明确接口
async action(@Payload() payload: { dto: SomeDto }) {
  const userId = payload.userId; // 正确
}

// 2. 单个@Payload装饰器
async action(@Payload() payload: EnhancedPayload) {
  // 所有字段都在payload中
}

// 3. 正确的缓存键格式
@Cacheable({
  key: 'service:#{payload.id}',
  serverId: '#{payload.serverId}'
})
```

## 🔍 调试技巧

### 查看注入字段

```typescript
@MessagePattern('debug.payload')
async debugPayload(@Payload() payload: any) {
  console.log('原始字段:', Object.keys(payload).filter(k => !['userId', 'wsContext', 'serverContext'].includes(k)));
  console.log('注入字段:', {
    userId: payload.userId,
    wsContext: payload.wsContext,
    serverContext: payload.serverContext,
  });
  return payload;
}
```

### 类型守卫

```typescript
function hasServerContext(payload: any): payload is EnhancedPayload & { 
  serverContext: NonNullable<EnhancedPayload['serverContext']> 
} {
  return payload.serverContext != null;
}

async someAction(@Payload() payload: EnhancedPayload) {
  if (hasServerContext(payload)) {
    // 现在可以安全访问serverContext
    const level = payload.serverContext.characterLevel;
  }
}
```

## 📊 性能优化

### 缓存策略

```typescript
// 多级缓存
@Cacheable({
  key: 'service:#{payload.id}',
  repository: 'memory', // L1缓存
  ttl: 300,
})
@Cacheable({
  key: 'service:#{payload.id}:#{payload.userId}',
  repository: 'redis', // L2缓存
  serverId: '#{payload.serverContext.serverId}',
  ttl: 3600,
})

// 条件缓存
@Cacheable({
  key: 'service:#{payload.id}',
  condition: '#{payload.serverContext.characterLevel > 5}', // 只缓存高级用户
  ttl: 1800,
})

// 批量操作
@Cacheable({
  key: 'service:batch:#{payload.ids}:#{payload.userId}',
  ttl: 600,
})
```

### 预热策略

```typescript
@MessagePattern('user.login')
async login(@Payload() payload: LoginPayload) {
  const result = await this.userService.login(payload);
  
  // 登录后预热缓存
  this.preWarmUserCache(payload.userId, payload.serverContext?.serverId);
  
  return result;
}
```

## 🔒 安全检查

### 权限验证

```typescript
async someAction(@Payload() payload: EnhancedPayload) {
  // 验证角色所有权
  if (payload.characterId && payload.serverContext?.characterId !== payload.characterId) {
    throw new ForbiddenException('角色权限不匹配');
  }
  
  // 验证区服权限
  if (!await this.validateServerAccess(payload.userId, payload.serverContext?.serverId)) {
    throw new ForbiddenException('区服访问权限不足');
  }
}
```

### 数据脱敏

```typescript
async getInfo(@Payload() payload: EnhancedPayload) {
  const data = await this.service.getData(payload.id);
  
  // 根据用户权限脱敏
  if (data.ownerId !== payload.userId) {
    delete data.sensitiveField;
  }
  
  return data;
}
```

## 🧪 测试模板

```typescript
describe('SomeController', () => {
  it('应该正确处理增强payload', async () => {
    const mockPayload = {
      // 原始字段
      id: 'test_id',
      name: 'test_name',
      
      // 注入字段
      userId: 'user_123',
      wsContext: {
        timestamp: Date.now(),
        routingStrategy: 'normal',
        messageId: 'msg_456',
      },
      serverContext: {
        serverId: 'server_001',
        characterId: 'char_789',
        characterLevel: 25,
      },
    };

    const result = await controller.someAction(mockPayload);
    
    expect(result.code).toBe(0);
    expect(service.someMethod).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 'test_id',
        userId: 'user_123',
      })
    );
  });
});
```

## 📚 相关文档

- [详细最佳实践指南](./websocket-payload-injection-best-practices.md)
- [微服务开发标准](./microservice-development-standards.md)
- [Redis缓存指南](../redis/redis-cache-best-practices.md)

---

**快速参考版本**: v1.0  
**最后更新**: 2025-01-02
