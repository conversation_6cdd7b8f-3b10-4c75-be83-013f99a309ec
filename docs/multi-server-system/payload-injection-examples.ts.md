```typescript

/**
 * WebSocket Payload注入实际代码示例
 * 
 * 本文件包含了各种场景下的实际代码示例，可以直接复制使用
 */

import { Controller, Logger, Injectable, BadRequestException, ForbiddenException } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { Cacheable, CacheEvict, CachePut } from '@common/redis';

// ==================== 接口定义 ====================

/**
 * 基础增强Payload接口
 */
interface BaseEnhancedPayload {
  userId: string;
  wsContext: {
    timestamp: number;
    routingStrategy: string;
    messageId?: string;
  };
  serverContext?: {
    serverId: string;
    characterId: string;
    serverName?: string;
    serverRegion?: string;
    characterLevel?: number;
    characterName?: string;
  };
  crossServerContext?: {
    sourceServerId: string;
    targetServerId: string;
    crossServerType: string;
  };
}

/**
 * 角色相关增强Payload
 */
interface CharacterEnhancedPayload extends BaseEnhancedPayload {
  characterId: string;
  serverId?: string;
}

/**
 * 搜索相关增强Payload
 */
interface SearchEnhancedPayload extends BaseEnhancedPayload {
  name: string;
  serverId?: string;
  limit?: number;
}

// ==================== DTO定义 ====================

export class LoginCharacterDto {
  userId: string;
  characterId: string;
  serverId: string;
  ip?: string;
  sessionId?: string;
}

export class UpdateCharacterDto {
  name?: string;
  level?: number;
  cash?: number;
  gold?: number;
}

export class CreateHeroDto {
  characterId: string;
  heroType: string;
  position: string;
}

// ==================== 类型守卫 ====================

/**
 * 检查是否有区服上下文
 */
function hasServerContext(payload: any): payload is BaseEnhancedPayload & { 
  serverContext: NonNullable<BaseEnhancedPayload['serverContext']> 
} {
  return payload.serverContext != null;
}

/**
 * 检查是否有跨服上下文
 */
function hasCrossServerContext(payload: any): payload is BaseEnhancedPayload & { 
  crossServerContext: NonNullable<BaseEnhancedPayload['crossServerContext']> 
} {
  return payload.crossServerContext != null;
}

// ==================== 示例Controller ====================

@Controller()
export class CharacterExampleController {
  private readonly logger = new Logger(CharacterExampleController.name);

  constructor(
    private readonly characterService: any,
    private readonly heroService: any
  ) {}

  /**
   * 示例1：角色登录 - 嵌套结构模式
   */
  @MessagePattern('character.login')
  @CachePut({
    key: 'character:info:#{payload.characterId}:#{payload.userId}',
    serverId: '#{payload.serverId}',
    ttl: 3600
  })
  async loginCharacter(@Payload() payload: { 
    loginDto: LoginCharacterDto;
    // 注入字段会自动添加到payload根级别
  }) {
    this.logger.log(`角色登录: ${payload.loginDto.characterId}, 用户: ${payload.userId}`);
    
    // 访问纯净的DTO
    const loginDto = payload.loginDto;
    
    // 访问注入的字段
    const userId = payload.userId;
    const serverContext = payload.serverContext;
    const loginTime = new Date(payload.wsContext.timestamp);
    
    const result = await this.characterService.loginCharacter({
      ...loginDto,
      userId,
      serverName: serverContext?.serverName,
      loginTime,
    });
    
    return { code: 0, message: '登录成功', data: result };
  }

  /**
   * 示例2：获取角色信息 - 明确接口模式
   */
  @MessagePattern('character.getInfo')
  @Cacheable({
    key: 'character:info:#{payload.characterId}:#{payload.userId}',
    serverId: '#{payload.serverContext.serverId}',
    condition: '#{payload.characterId != null}',
    ttl: 3600
  })
  async getCharacterInfo(@Payload() payload: CharacterEnhancedPayload) {
    this.logger.log(`获取角色信息: ${payload.characterId}`);
    
    // 类型安全访问
    const characterId = payload.characterId;
    const userId = payload.userId;
    const serverName = payload.serverContext?.serverName;
    
    const character = await this.characterService.getCharacterInfo(characterId, userId);
    
    return { code: 0, message: '获取成功', data: character };
  }

  /**
   * 示例3：搜索角色 - 复杂缓存键
   */
  @MessagePattern('character.searchByName')
  @Cacheable({
    key: 'character:search:name:#{payload.name}:#{payload.serverId}:#{payload.userId}',
    serverId: '#{payload.serverId}',
    condition: '#{payload.name != null && payload.serverContext.characterLevel > 0}',
    ttl: 300
  })
  async searchCharacterByName(@Payload() payload: SearchEnhancedPayload) {
    this.logger.log(`搜索角色: ${payload.name}, 搜索者: ${payload.userId}`);
    
    // 智能字段选择
    const serverId = payload.serverId || payload.serverContext?.serverId;
    const searchLimit = payload.limit || 10;
    
    const characters = await this.characterService.searchByName(
      payload.name,
      serverId,
      payload.userId,
      searchLimit
    );
    
    return { code: 0, message: '搜索成功', data: characters };
  }

  /**
   * 示例4：更新角色 - 组合缓存操作
   */
  @MessagePattern('character.update')
  @CachePut({
    key: 'character:info:#{payload.characterId}:#{payload.userId}',
    serverId: '#{payload.serverContext.serverId}',
    ttl: 3600
  })
  @CacheEvict({
    key: [
      'character:search:*:#{payload.serverContext.serverId}:#{payload.userId}',
      'user:characters:#{payload.userId}'
    ],
    dataType: 'server',
    serverId: '#{payload.serverContext.serverId}'
  })
  async updateCharacter(@Payload() payload: {
    characterId: string;
    updateDto: UpdateCharacterDto;
    serverId?: string;
  }) {
    this.logger.log(`更新角色: ${payload.characterId}, 操作者: ${payload.userId}`);
    
    // 权限检查
    if (!hasServerContext(payload)) {
      throw new BadRequestException('需要角色Token');
    }
    
    if (payload.serverContext.characterId !== payload.characterId) {
      throw new ForbiddenException('只能更新自己的角色');
    }
    
    const character = await this.characterService.updateCharacter(
      payload.characterId,
      payload.updateDto,
      payload.userId
    );
    
    return { code: 0, message: '更新成功', data: character };
  }

  /**
   * 示例5：跨服操作 - 条件性上下文处理
   */
  @MessagePattern('character.crossServerBattle')
  async crossServerBattle(@Payload() payload: BaseEnhancedPayload & {
    targetCharacterId: string;
    battleType: string;
  }) {
    this.logger.log(`跨服战斗: ${payload.userId} vs ${payload.targetCharacterId}`);
    
    // 检查必要的上下文
    if (!hasServerContext(payload)) {
      throw new BadRequestException('需要角色Token');
    }
    
    if (!hasCrossServerContext(payload)) {
      throw new BadRequestException('需要跨服路由');
    }
    
    // 根据路由策略执行不同逻辑
    switch (payload.wsContext.routingStrategy) {
      case 'cross_server':
        return this.handleCrossServerBattle(payload);
      case 'global':
        return this.handleGlobalBattle(payload);
      default:
        throw new BadRequestException('不支持的路由策略');
    }
  }

  private async handleCrossServerBattle(payload: any) {
    const sourceServer = payload.crossServerContext.sourceServerId;
    const targetServer = payload.crossServerContext.targetServerId;
    
    this.logger.log(`跨服战斗: ${sourceServer} -> ${targetServer}`);
    
    // 跨服战斗逻辑
    const result = await this.characterService.executeCrossServerBattle(
      payload.serverContext.characterId,
      payload.targetCharacterId,
      sourceServer,
      targetServer
    );
    
    return { code: 0, message: '跨服战斗完成', data: result };
  }

  private async handleGlobalBattle(payload: any) {
    this.logger.log(`全服战斗: ${payload.serverContext.characterId}`);
    
    // 全服战斗逻辑
    const result = await this.characterService.executeGlobalBattle(
      payload.serverContext.characterId,
      payload.targetCharacterId
    );
    
    return { code: 0, message: '全服战斗完成', data: result };
  }
}

// ==================== Hero服务示例 ====================

@Controller()
export class HeroExampleController {
  private readonly logger = new Logger(HeroExampleController.name);

  constructor(private readonly heroService: any) {}

  /**
   * 示例6：创建英雄 - 利用注入字段
   */
  @MessagePattern('hero.create')
  @CacheEvict({
    key: 'character:heroes:#{payload.characterId}',
    dataType: 'server',
    serverId: '#{payload.serverId}'
  })
  async createHero(@Payload() payload: { createDto: CreateHeroDto }) {
    this.logger.log(`创建英雄: ${JSON.stringify(payload.createDto)}, 用户: ${payload.userId}`);
    
    // 权限检查：确保用户只能为自己的角色创建英雄
    if (hasServerContext(payload)) {
      if (payload.serverContext.characterId !== payload.createDto.characterId) {
        throw new ForbiddenException('只能为自己的角色创建英雄');
      }
    }
    
    const hero = await this.heroService.createHero({
      ...payload.createDto,
      createdBy: payload.userId,
      createdAt: new Date(payload.wsContext.timestamp),
      serverId: payload.serverContext?.serverId,
    });
    
    return { code: 0, message: '英雄创建成功', data: hero };
  }

  /**
   * 示例7：获取英雄列表 - 多级缓存
   */
  @MessagePattern('hero.getList')
  @Cacheable({
    key: 'character:heroes:#{payload.characterId}',
    repository: 'memory',
    ttl: 300,
  })
  @Cacheable({
    key: 'character:heroes:#{payload.characterId}:#{payload.userId}',
    repository: 'redis',
    serverId: '#{payload.serverContext.serverId}',
    ttl: 1800,
  })
  async getHeroList(@Payload() payload: CharacterEnhancedPayload) {
    this.logger.log(`获取英雄列表: ${payload.characterId}`);
    
    const heroes = await this.heroService.getHeroList(
      payload.characterId,
      payload.userId
    );
    
    return { code: 0, message: '获取成功', data: heroes };
  }

  /**
   * 示例8：批量操作 - 性能优化
   */
  @MessagePattern('hero.getBatch')
  @Cacheable({
    key: 'hero:batch:#{payload.heroIds}:#{payload.userId}',
    serverId: '#{payload.serverContext.serverId}',
    condition: '#{payload.heroIds != null}',
    ttl: 600,
  })
  async getBatchHeroes(@Payload() payload: {
    heroIds: string[];
    serverId?: string;
  }) {
    this.logger.log(`批量获取英雄: ${payload.heroIds.length}个英雄`);
    
    // 利用注入的用户上下文进行权限过滤
    const allowedHeroes = await this.heroService.filterByPermission(
      payload.heroIds,
      payload.userId
    );
    
    const heroes = await this.heroService.getBatchHeroes(allowedHeroes);
    
    return { code: 0, message: '获取成功', data: heroes };
  }
}

// ==================== 工具函数示例 ====================

/**
 * 数据脱敏工具
 */
export function sanitizeDataByPermission<T>(
  data: T,
  ownerId: string,
  currentUserId: string,
  userLevel?: number
): Partial<T> {
  const result = { ...data };
  
  // 如果不是数据所有者，移除敏感信息
  if (ownerId !== currentUserId) {
    delete (result as any).email;
    delete (result as any).phone;
    delete (result as any).realName;
    delete (result as any).privateNotes;
  }
  
  // 根据用户等级脱敏
  if ((userLevel || 0) < 10) {
    delete (result as any).advancedStats;
    delete (result as any).internalData;
  }
  
  return result;
}

/**
 * 权限验证工具
 */
export async function validatePayloadPermissions(
  payload: BaseEnhancedPayload,
  requiredLevel: number = 0
): Promise<void> {
  // 验证基础权限
  if (!payload.userId) {
    throw new BadRequestException('缺少用户身份信息');
  }
  
  // 验证角色权限
  if (hasServerContext(payload)) {
    const characterLevel = payload.serverContext.characterLevel || 0;
    if (characterLevel < requiredLevel) {
      throw new ForbiddenException(`需要角色等级 ${requiredLevel} 以上`);
    }
  }
  
  // 验证区服权限
  if (payload.serverContext) {
    const hasServerAccess = await validateServerAccess(
      payload.userId,
      payload.serverContext.serverId
    );
    if (!hasServerAccess) {
      throw new ForbiddenException('区服访问权限不足');
    }
  }
}

/**
 * 模拟区服权限验证
 */
async function validateServerAccess(userId: string, serverId: string): Promise<boolean> {
  // 实际实现中应该查询数据库或缓存
  return true;
}

// ==================== 测试工具 ====================

/**
 * 创建测试用的增强Payload
 */
export function createMockEnhancedPayload(
  originalData: any,
  options: {
    userId?: string;
    characterId?: string;
    serverId?: string;
    characterLevel?: number;
    routingStrategy?: string;
  } = {}
): BaseEnhancedPayload {
  return {
    ...originalData,
    userId: options.userId || 'test_user_123',
    wsContext: {
      timestamp: Date.now(),
      routingStrategy: options.routingStrategy || 'normal',
      messageId: `msg_${Date.now()}`,
    },
    serverContext: options.characterId ? {
      serverId: options.serverId || 'server_001',
      characterId: options.characterId,
      serverName: '测试区服',
      serverRegion: 'test-region',
      characterLevel: options.characterLevel || 1,
      characterName: '测试角色',
    } : undefined,
  };
}

/**
 * 测试用例示例
 */
export const testExamples = {
  // 基础角色操作测试
  characterLogin: createMockEnhancedPayload(
    { loginDto: { userId: 'user_123', characterId: 'char_456', serverId: 'server_001' } },
    { userId: 'user_123', characterId: 'char_456', serverId: 'server_001', characterLevel: 10 }
  ),
  
  // 搜索操作测试
  characterSearch: createMockEnhancedPayload(
    { name: '测试角色', serverId: 'server_001' },
    { userId: 'user_123', characterId: 'char_456', characterLevel: 5 }
  ),
  
  // 跨服操作测试
  crossServerBattle: {
    ...createMockEnhancedPayload(
      { targetCharacterId: 'char_789', battleType: 'pvp' },
      { userId: 'user_123', characterId: 'char_456', routingStrategy: 'cross_server' }
    ),
    crossServerContext: {
      sourceServerId: 'server_001',
      targetServerId: 'server_002',
      crossServerType: 'battle',
    },
  },
};
```