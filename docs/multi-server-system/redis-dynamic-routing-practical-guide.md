# Redis动态路由实战指南

## 🎯 **指南目标**

基于我们对Redis公共库的深度分析，提供实际开发中的最佳实践和常见场景的解决方案。

## 🚀 **快速开始**

### **1. 基础配置**

```typescript
// apps/character/src/app.module.ts
@Module({
  imports: [
    // ✅ 推荐：使用环境变量配置
    RedisModule.forRootAsync({
      service: 'character',
      serverId: process.env.SERVER_ID || 'server_001', // 从.env.server读取
    }),
  ],
})
export class AppModule {}
```

### **2. 环境变量配置**

```bash
# .env.server
SERVER_ID=server_001
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=123456
```

## 📋 **常见使用场景**

### **场景1：单区服数据访问（90%的场景）**

```typescript
// ✅ 推荐：不指定serverId，使用默认值
@Controller('character')
export class CharacterController {
  
  @MessagePattern('character.getInfo')
  @Cacheable({
    key: 'character:info:#{payload.characterId}',
    // 不指定serverId，自动使用模块配置的server_001
    ttl: 3600
  })
  async getCharacterInfo(@Payload() payload: { characterId: string }) {
    // Redis键：development:fm:serverserver_001:character:character:info:char123
    return await this.characterService.getCharacterInfo(payload.characterId);
  }

  @MessagePattern('character.updateInfo')
  @CachePut({
    key: 'character:info:#{payload.characterId}',
    // 同样不指定serverId
    ttl: 3600
  })
  async updateCharacterInfo(@Payload() payload: UpdateCharacterDto) {
    return await this.characterService.updateCharacterInfo(payload);
  }
}
```

### **场景2：跨区服数据访问（特殊场景）**

```typescript
// ✅ 跨服PVP战斗系统
@Controller('battle')
export class BattleController {
  
  @MessagePattern('battle.crossServerBattle')
  async crossServerBattle(@Payload() payload: {
    myCharacterId: string;
    enemyCharacterId: string;
    enemyServerId: string; // 对手所在区服
  }) {
    // 获取我的角色（使用默认区服）
    const myCharacter = await this.getMyCharacter(payload.myCharacterId);
    
    // 获取对手角色（指定区服）
    const enemyCharacter = await this.getEnemyCharacter(
      payload.enemyCharacterId, 
      payload.enemyServerId
    );
    
    return await this.executeBattle(myCharacter, enemyCharacter);
  }

  @Cacheable({
    key: 'character:info:#{characterId}',
    // 不指定serverId，使用默认区服
    ttl: 3600
  })
  private async getMyCharacter(characterId: string) {
    return await this.characterService.getCharacterInfo(characterId);
  }

  @Cacheable({
    key: 'character:info:#{characterId}',
    serverId: '#{serverId}', // 🎯 动态指定目标区服
    ttl: 3600
  })
  private async getEnemyCharacter(characterId: string, serverId: string) {
    // Redis键：development:fm:server{serverId}:character:character:info:char123
    return await this.characterService.getCharacterFromServer(characterId, serverId);
  }
}
```

### **场景3：管理后台全区服查询**

```typescript
// ✅ 管理后台：查询所有区服的数据
@Controller('admin')
export class AdminController {
  
  @Get('characters/search')
  async searchAllServers(@Query('name') name: string) {
    const allServers = ['server_001', 'server_002', 'server_003'];
    const results = [];
    
    for (const serverId of allServers) {
      const characters = await this.searchCharactersInServer(name, serverId);
      results.push({
        serverId,
        characters
      });
    }
    
    return results;
  }

  @Cacheable({
    key: 'character:search:#{name}',
    serverId: '#{serverId}', // 🎯 动态查询不同区服
    ttl: 300
  })
  private async searchCharactersInServer(name: string, serverId: string) {
    // 在指定区服中搜索角色
    return await this.characterService.searchByName(name, serverId);
  }
}
```

### **场景4：全服排行榜系统**

```typescript
// ✅ 排行榜：需要处理全服和区服两种数据
@Controller('ranking')
export class RankingController {
  
  @MessagePattern('ranking.updateScore')
  async updateScore(@Payload() payload: {
    characterId: string;
    score: number;
    serverId: string;
  }) {
    // 1. 更新全服排行榜
    await this.updateGlobalRanking(payload);
    
    // 2. 更新区服排行榜
    await this.updateServerRanking(payload);
    
    // 3. 更新其他区服的缓存（如果需要）
    await this.invalidateOtherServers(payload);
  }

  @CachePut({
    key: 'ranking:global:#{characterId}',
    dataType: 'global', // 🎯 全服数据，不需要serverId
    ttl: 1800
  })
  private async updateGlobalRanking(payload: any) {
    // Redis键：development:fm:global:character:ranking:global:char123
    return await this.rankingService.updateGlobalScore(payload);
  }

  @CachePut({
    key: 'ranking:server:#{characterId}',
    serverId: '#{serverId}', // 🎯 指定区服
    ttl: 1800
  })
  private async updateServerRanking(payload: any) {
    // Redis键：development:fm:server{serverId}:character:ranking:server:char123
    return await this.rankingService.updateServerScore(payload);
  }
}
```

### **场景5：数据迁移和同步**

```typescript
// ✅ 数据迁移：在区服间转移数据
@Controller('migration')
export class MigrationController {
  
  @MessagePattern('migration.migrateCharacter')
  async migrateCharacter(@Payload() payload: {
    characterId: string;
    fromServerId: string;
    toServerId: string;
  }) {
    // 1. 从源区服读取数据
    const characterData = await this.getCharacterFromServer(
      payload.characterId, 
      payload.fromServerId
    );
    
    // 2. 保存到目标区服
    await this.saveCharacterToServer(
      characterData, 
      payload.toServerId
    );
    
    // 3. 清除源区服缓存
    await this.clearCharacterCache(
      payload.characterId, 
      payload.fromServerId
    );
    
    return { success: true };
  }

  @Cacheable({
    key: 'character:info:#{characterId}',
    serverId: '#{serverId}', // 🎯 从指定区服读取
    ttl: 3600
  })
  private async getCharacterFromServer(characterId: string, serverId: string) {
    return await this.characterService.getCharacterFromServer(characterId, serverId);
  }

  @CachePut({
    key: 'character:info:#{characterData.id}',
    serverId: '#{serverId}', // 🎯 保存到指定区服
    ttl: 3600
  })
  private async saveCharacterToServer(characterData: any, serverId: string) {
    return await this.characterService.saveCharacterToServer(characterData, serverId);
  }

  @CacheEvict({
    key: 'character:info:#{characterId}',
    serverId: '#{serverId}', // 🎯 清除指定区服的缓存
  })
  private async clearCharacterCache(characterId: string, serverId: string) {
    // 精确清除指定区服的缓存
  }
}
```

## 🔧 **高级用法**

### **1. 利用WebSocket Payload注入**

```typescript
// ✅ 利用网关注入的区服上下文
@MessagePattern('character.getInfo')
@Cacheable({
  key: 'character:info:#{payload.characterId}',
  serverId: '#{payload.serverContext.serverId}', // 🎯 从注入上下文获取
  ttl: 3600
})
async getCharacterInfo(@Payload() payload: EnhancedPayload) {
  // payload.serverContext.serverId 由WebSocket网关自动注入
  // 更可靠，不依赖客户端传递的serverId
}
```

### **2. 条件缓存**

```typescript
// ✅ 基于区服和角色等级的条件缓存
@Cacheable({
  key: 'character:info:#{payload.characterId}',
  serverId: '#{payload.serverContext.serverId}',
  condition: '#{payload.serverContext.characterLevel > 10}', // 🎯 条件缓存
  ttl: 3600
})
async getCharacterInfo(@Payload() payload: EnhancedPayload) {
  // 只有角色等级大于10才缓存
}
```

### **3. 多级缓存策略**

```typescript
// ✅ 本地缓存 + Redis缓存
@Controller('character')
export class CharacterController {
  
  @Cacheable({
    key: 'character:info:#{payload.characterId}',
    serverId: '#{payload.serverContext.serverId}',
    ttl: 3600, // Redis缓存1小时
    localTtl: 300, // 本地缓存5分钟
  })
  async getCharacterInfo(@Payload() payload: EnhancedPayload) {
    // 先查本地缓存，再查Redis，最后查数据库
  }
}
```

## ⚠️ **常见陷阱与解决方案**

### **陷阱1：忘记处理跨区服场景**

```typescript
// ❌ 错误：只考虑本区服
@Cacheable({
  key: 'character:info:#{payload.characterId}',
  // 缺少serverId，无法处理跨区服请求
  ttl: 3600
})
async getCharacterInfo(characterId: string) {
  // 只能访问本区服数据
}

// ✅ 正确：考虑跨区服场景
@Cacheable({
  key: 'character:info:#{payload.characterId}',
  serverId: '#{payload.targetServerId || payload.serverContext.serverId}', // 支持跨区服
  ttl: 3600
})
async getCharacterInfo(@Payload() payload: GetCharacterDto) {
  // 可以访问指定区服或默认区服的数据
}
```

### **陷阱2：硬编码区服ID**

```typescript
// ❌ 错误：硬编码区服ID
@Cacheable({
  key: 'character:info:#{payload.characterId}',
  serverId: 'server_001', // 硬编码，不灵活
  ttl: 3600
})

// ✅ 正确：使用动态表达式
@Cacheable({
  key: 'character:info:#{payload.characterId}',
  serverId: '#{payload.serverId}', // 动态表达式
  ttl: 3600
})
```

### **陷阱3：缓存键冲突**

```typescript
// ❌ 错误：可能导致不同区服数据冲突
@Cacheable({
  key: 'character:#{payload.characterId}', // 缺少区服信息
  ttl: 3600
})

// ✅ 正确：包含完整的上下文信息
@Cacheable({
  key: 'character:info:#{payload.characterId}:#{payload.userId}', // 包含用户ID
  serverId: '#{payload.serverContext.serverId}', // 明确区服
  ttl: 3600
})
```

## 📊 **性能优化建议**

### **1. 缓存TTL策略**

```typescript
// ✅ 根据数据特性设置不同的TTL
@Cacheable({
  key: 'character:basic:#{payload.characterId}',
  serverId: '#{payload.serverContext.serverId}',
  ttl: 3600, // 基础信息：1小时
})
async getCharacterBasicInfo() {}

@Cacheable({
  key: 'character:stats:#{payload.characterId}',
  serverId: '#{payload.serverContext.serverId}',
  ttl: 300, // 战斗属性：5分钟
})
async getCharacterStats() {}

@Cacheable({
  key: 'character:realtime:#{payload.characterId}',
  serverId: '#{payload.serverContext.serverId}',
  ttl: 60, // 实时数据：1分钟
})
async getCharacterRealtimeData() {}
```

### **2. 批量操作优化**

```typescript
// ✅ 批量获取多个区服的数据
async getCharactersFromMultipleServers(characterIds: string[], serverIds: string[]) {
  const promises = [];
  
  for (const serverId of serverIds) {
    for (const characterId of characterIds) {
      promises.push(this.getCharacterFromServer(characterId, serverId));
    }
  }
  
  return await Promise.all(promises);
}
```

## 🎯 **总结**

### **核心原则**

1. **默认使用模块serverId**：90%的场景不需要显式指定
2. **跨区服场景显式指定**：明确指定目标区服
3. **利用payload注入**：使用网关注入的区服上下文
4. **避免硬编码**：使用动态表达式和环境变量

### **最佳实践清单**

- ✅ 使用环境变量配置模块serverId
- ✅ 大部分场景不指定装饰器serverId
- ✅ 跨区服场景显式指定serverId
- ✅ 利用WebSocket payload注入的区服上下文
- ✅ 设置合理的缓存TTL
- ✅ 包含完整的上下文信息在缓存键中
- ✅ 考虑条件缓存和多级缓存
- ❌ 避免硬编码区服ID
- ❌ 避免缓存键冲突
- ❌ 避免过度复杂的表达式

---

**文档版本**: v1.0  
**适用范围**: Redis公共库 v2.0+  
**最后更新**: 2025-01-02
