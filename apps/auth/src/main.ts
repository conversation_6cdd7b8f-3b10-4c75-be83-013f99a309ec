import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON>tionPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { WinstonModule } from 'nest-winston';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { CacheInterceptor } from '@common/redis';

import * as helmet from 'helmet';
import * as compression from 'compression';
import { AppModule } from './app.module';
import { createWinstonLogger } from '@auth/common/utils/logger.util';

async function bootstrap() {
  // 1. 创建应用实例
  const app = await NestFactory.create(AppModule, {
    logger: WinstonModule.createLogger(createWinstonLogger()),
  });

  const configService = app.get(ConfigService);
  const logger = new Logger('Bootstrap');

  // 2. 获取配置
  const port = configService.get('AUTH_PORT', 3001);
  const environment = configService.get('NODE_ENV', 'development');

  // 3. 安全中间件
  app.use(helmet.default({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true,
    },
  }));

  // 4. 压缩中间件
  app.use(compression());

  // 5. CORS 配置
  app.enableCors({
    origin: configService.get<string[]>('security.cors.origin', ['http://localhost:3000']),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-API-Key',
      'X-Request-ID',
    ],
    exposedHeaders: [
      'X-RateLimit-Limit',
      'X-RateLimit-Remaining',
      'X-RateLimit-Reset',
      'X-Response-Time',
    ],
    credentials: true,
    maxAge: 86400, // 24小时
  });

  // 6. 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // 自动移除非装饰器属性
      forbidNonWhitelisted: true, // 禁止非白名单属性
      transform: true, // 自动转换类型
      disableErrorMessages: environment === 'production', // 生产环境隐藏详细错误
      validationError: {
        target: false, // 不返回目标对象
        value: false, // 不返回值
      },
    }),
  );

  // 7. 全局缓存拦截器
  app.useGlobalInterceptors(app.get(CacheInterceptor));

  // 8. Swagger API 文档
  if (environment !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('足球认证服务')
      .setDescription('提供用户认证、授权和会话管理的微服务API')
      .setVersion('1.0.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-API-Key',
          in: 'header',
          description: 'API Key for service-to-service authentication',
        },
        'API-Key',
      )
      .addTag('认证', '用户认证相关接口')
      .addTag('用户', '用户管理相关接口')
      .addTag('角色', '角色管理相关接口')
      .addTag('权限', '权限管理相关接口')
      .addTag('管理', '管理员功能接口')
      .addTag('安全', '安全相关接口')
      .addServer(`http://localhost:${port}`, '本地开发环境')
      .addServer('https://api-dev.yourgame.com', '开发环境')
      .addServer('https://api.yourgame.com', '生产环境')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
      customSiteTitle: '足球认证服务 API',
      customfavIcon: '/favicon.ico',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info .title { color: #1976d2 }
      `,
    });

    logger.log(`📚 API文档已启用: http://localhost:${port}/docs`);
  }

  // 9. 优雅关闭
  process.on('SIGTERM', async () => {
    logger.log('🔄 收到SIGTERM信号，开始优雅关闭...');
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('🔄 收到SIGINT信号，开始优雅关闭...');
    await app.close();
    process.exit(0);
  });

  // 10. 使用 ConfigService 获取微服务配置
  const microserviceConfig = configService.get('microserviceKit');
  const serviceConfig = microserviceConfig?.services[MICROSERVICE_NAMES.AUTH_SERVICE];

  if (serviceConfig) {
    const microserviceOptions = {
      transport: serviceConfig.transport,
      options: serviceConfig.options,
    };

    // 调试日志：显示微服务配置
    logger.log(`🔍 微服务配置调试:`);
    logger.log(`📡 传输方式: ${serviceConfig.transport}`);
    logger.log(`🏠 Redis 主机: ${serviceConfig.options.host}`);
    logger.log(`🔌 Redis 端口: ${serviceConfig.options.port}`);
    logger.log(`🗄️ Redis 数据库: ${serviceConfig.options.db}`);
    logger.log(`🔑 Redis 密码: ${serviceConfig.options.password ? '***' : '未设置'}`);

    // 连接微服务传输层
    app.connectMicroservice(microserviceOptions);

    // 启动所有微服务
    await app.startAllMicroservices();
    logger.log(`🔗 微服务传输层已启动 (${serviceConfig.transport})`);
  } else {
    logger.error(`❌ 未找到微服务配置: ${MICROSERVICE_NAMES.AUTH_SERVICE}`);
    logger.error(`📋 可用配置: ${Object.keys(microserviceConfig?.services || {}).join(', ')}`);
  }

  // 11. 启动HTTP服务
  await app.listen(port, '0.0.0.0');

  logger.log(`🚀 认证服务已启动`);
  logger.log(`📍 HTTP端口: ${port}`);
  logger.log(`🔗 微服务: Redis传输层`);
  logger.log(`🌍 环境: ${environment}`);
  logger.log(`🔗 健康检查: http://localhost:${port}/health`);

  if (environment !== 'production') {
    logger.log(`📚 API文档: http://localhost:${port}/docs`);
  }
}

// 启动应用
bootstrap().catch((error) => {
  console.error('❌ 应用启动失败:', error);
  process.exit(1);
});
