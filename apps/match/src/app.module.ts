import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@app/database/mongodb.config';
import { RedisModule } from '@common/redis';
import { GameConfigModule } from '@app/game-config';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';

// 业务模块
import { LeagueModule } from './modules/league/league.module';
import { BusinessModule } from './modules/business/business.module';
import { TrophyModule } from './modules/trophy/trophy.module';
import { TournamentModule } from './modules/tournament/tournament.module';
import { BattleModule } from './modules/battle/battle.module';
import { RankingModule } from './modules/ranking/ranking.module';
import { HealthModule } from './modules/health/health.module';

// 共享服务导入
import { BattleDataService } from './common/services/battle-data.service';

/**
 * 比赛系统微服务主模块
 * 
 * 核心功能：
 * - 联赛副本系统 (基于old项目leagueCopy.js)
 * - 商业赛系统 (基于old项目businessMatch.js)
 * - 杯赛系统 (基于old项目trophyCopy.js)
 * - 锦标赛系统 (基于old项目worldCup.js等)
 * - 战斗计算引擎 (基于old项目battleService.js)
 * - 排名系统
 * 
 * 微服务通信：
 * - 需要调用character服务（获取角色信息、阵容数据）
 * - 需要调用hero服务（获取球员数据、计算战斗力）
 * - 需要调用economy服务（奖励发放、货币操作）
 * - 需要调用activity服务（任务进度更新）
 */
@Module({
  imports: [
    // 1. 配置模块（最高优先级）
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`,  // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/match/.env.server',                    // 4. 区服配置
        'apps/match/.env.redis',                     // 5. Redis业务配置
        'apps/match/.env.database',                  // 6. 数据库配置
        'apps/match/.env.security',                  // 7. 安全业务配置

        // 环境特定的业务配置（如果存在）
        `apps/match/.env.server.${process.env.NODE_ENV || 'development'}`,
        `apps/match/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/match/.env.database.${process.env.NODE_ENV || 'development'}`,
        `apps/match/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/match/.env.local',                     // 8. 服务本地覆盖
        ],
      cache: true,
    }),

    // 2. 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, 'match'),
        ...setupDatabaseEvents('match'),
      }),
      inject: [ConfigService],
    }),

    // 3. Redis模块
    RedisModule.forRootAsync({
      service: 'match',
      serverId: 'server_001', // 默认区服ID
    }),

    // 4. 游戏配置模块（核心基础设施）
    GameConfigModule.forRootAsync(),

    // 5. 微服务公共库 - 混合模式（既是服务端又是客户端）
    MicroserviceKitModule.forHybrid(
      MICROSERVICE_NAMES.MATCH_SERVICE,  // 作为服务端的服务名
      {
        services: [                               // 作为客户端需要连接的服务
          MICROSERVICE_NAMES.CHARACTER_SERVICE,   // 需要调用角色服务（获取角色信息、阵容数据）
          MICROSERVICE_NAMES.HERO_SERVICE,        // 需要调用球员服务（获取球员数据、计算战斗力）
          MICROSERVICE_NAMES.ECONOMY_SERVICE,     // 需要调用经济服务（奖励发放、货币操作）
          MICROSERVICE_NAMES.ACTIVITY_SERVICE,    // 需要调用活动服务（任务进度更新）
        ],
      }
    ),

    // 6. 业务模块（注意：BattleModule必须在依赖它的模块之前注册）
    BattleModule,      // 战斗计算引擎（基础模块，其他模块依赖）
    LeagueModule,      // 联赛副本系统（依赖BattleModule）
    BusinessModule,    // 商业赛系统（依赖BattleModule）
    TrophyModule,      // 杯赛系统
    TournamentModule,  // 锦标赛系统
    RankingModule,     // 排名系统
    HealthModule,      // 健康检查
  ],
  controllers: [],
  providers: [
    // 共享服务
    BattleDataService,
  ],
  exports: [
    // 导出共享服务供其他模块使用
    BattleDataService,
  ],
})
export class AppModule {}
