import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';

/**
 * 比赛服务健康检查服务
 * 监控数据库连接、Redis连接和服务状态
 */
@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    @InjectConnection() private readonly mongoConnection: Connection,
  ) {}

  /**
   * 获取基础健康状态
   */
  async getHealthStatus() {
    try {
      const dbStatus = this.mongoConnection.readyState === 1 ? 'healthy' : 'unhealthy';
      
      return {
        status: dbStatus === 'healthy' ? 'ok' : 'error',
        timestamp: new Date().toISOString(),
        service: 'match',
        version: '1.0.0',
        uptime: process.uptime(),
        database: {
          status: dbStatus,
          name: this.mongoConnection.name,
        },
      };
    } catch (error) {
      this.logger.error('健康检查失败:', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        service: 'match',
        error: error.message,
      };
    }
  }

  /**
   * 获取详细健康状态
   */
  async getDetailedHealthStatus() {
    try {
      const basicHealth = await this.getHealthStatus();
      
      return {
        ...basicHealth,
        details: {
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          environment: process.env.NODE_ENV,
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch,
        },
        modules: {
          league: 'ready',      // 联赛系统状态
          business: 'ready',    // 商业赛系统状态
          trophy: 'ready',      // 杯赛系统状态
          tournament: 'ready',  // 锦标赛系统状态
          battle: 'ready',      // 战斗引擎状态
          ranking: 'ready',     // 排名系统状态
        },
      };
    } catch (error) {
      this.logger.error('详细健康检查失败:', error);
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        service: 'match',
        error: error.message,
      };
    }
  }

  /**
   * 获取服务信息
   */
  getServiceInfo() {
    return {
      name: 'football-manager-match',
      version: '1.0.0',
      description: '足球比赛系统微服务',
      author: 'Football Manager Team',
      license: 'MIT',
      repository: 'https://github.com/football-manager/server-new',
      features: [
        '联赛副本系统',
        '商业赛系统', 
        '杯赛系统',
        '锦标赛系统',
        '战斗计算引擎',
        '排名系统',
      ],
      dependencies: {
        character: '需要调用角色服务获取角色信息和阵容数据',
        hero: '需要调用球员服务获取球员数据和计算战斗力',
        economy: '需要调用经济服务进行奖励发放和货币操作',
        activity: '需要调用活动服务更新任务进度（暂时注释，待Activity服务测试完成后开启）',
      },
    };
  }
}
