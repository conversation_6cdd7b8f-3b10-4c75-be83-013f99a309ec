import { registerAs } from '@nestjs/config';
import * as <PERSON><PERSON> from 'joi';
import { MICROSERVICE_NAMES } from '@shared/constants';

export const appConfig = registerAs('app', () => {
  const config = {
    name: process.env.APP_NAME || 'football-manager-economy',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.ECONOMY_PORT || '3009', 10),
    host: process.env.ECONOMY_HOST || '0.0.0.0',
    timezone: process.env.TZ || 'UTC',
    
    // 服务发现配置
    service: {
      name: MICROSERVICE_NAMES.ECONOMY_SERVICE,
      version: '1.0.0',
      description: '足球经济服务',
      tags: ['economy', 'shop', 'payment', 'currency', 'trade', 'exchange', 'relay'],
    },

    // 应用特性开关
    features: {
      swagger: process.env.ENABLE_SWAGGER !== 'false',
      metrics: process.env.ENABLE_METRICS !== 'false',
      healthCheck: process.env.ENABLE_HEALTH_CHECK !== 'false',
      gracefulShutdown: process.env.ENABLE_GRACEFUL_SHUTDOWN !== 'false',
    },

    // 性能配置
    performance: {
      maxRequestSize: process.env.MAX_REQUEST_SIZE || '10mb',
      requestTimeout: parseInt(process.env.REQUEST_TIMEOUT || '30000', 10),
      keepAliveTimeout: parseInt(process.env.KEEP_ALIVE_TIMEOUT || '5000', 10),
    },

    // 安全配置
    security: {
      cors: {
        origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
        credentials: process.env.CORS_CREDENTIALS === 'true',
      },
      helmet: {
        enabled: process.env.HELMET_ENABLED !== 'false',
      },
      rateLimit: {
        enabled: process.env.RATE_LIMIT_ENABLED !== 'false',
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000', 10),
        max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
      },
    },

    // 日志配置
    logging: {
      level: process.env.LOG_LEVEL || 'info',
      format: process.env.LOG_FORMAT || 'json',
      enableConsole: process.env.LOG_CONSOLE !== 'false',
      enableFile: process.env.LOG_FILE === 'true',
      filePath: process.env.LOG_FILE_PATH || './logs/economy.log',
    },
  };

  // 验证配置（只验证核心字段，允许其他字段）
  const schema = Joi.object({
    name: Joi.string().required(),
    version: Joi.string().required(),
    environment: Joi.string().valid('development', 'staging', 'production').required(),
    port: Joi.number().port().required(),
    host: Joi.string().required(),
    timezone: Joi.string().required(),
  }).unknown(true); // 允许其他未定义的字段

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`Economy服务配置验证失败: ${error.message}`);
  }

  return config;
});

export default appConfig;
