import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { CacheInterceptor } from '@common/redis';
import { AppModule } from './app.module';


async function bootstrap() {
  // 1. 创建应用实例
  const app = await NestFactory.create(AppModule);

  const configService = app.get(ConfigService);
  const logger = new Logger('CharacterService');

  // 2. 获取配置
  const port = configService.get('CHARACTER_PORT', 3002);
  const environment = configService.get('NODE_ENV', 'development');

  // 8. Swagger API 文档
  if (environment !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('足球经理角色服务')
      .setDescription('提供角色管理、阵容配置、物品管理和战术设置的微服务API')
      .setVersion('1.0.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-API-Key',
          in: 'header',
          description: 'API Key for service-to-service authentication',
        },
        'API-Key',
      )
      .addTag('角色', '角色管理相关接口')
      .addTag('阵容', '阵容配置相关接口')
      .addTag('物品', '物品管理相关接口')
      .addTag('背包', '背包系统相关接口')
      .addTag('战术', '战术设置相关接口')
      .addTag('健康检查', '服务健康状态接口')
      .addServer(`http://localhost:${port}`, '本地开发环境')
      .addServer('https://api-dev.yourgame.com', '开发环境')
      .addServer('https://api.yourgame.com', '生产环境')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
      customSiteTitle: '足球经理角色服务 API',
      customfavIcon: '/favicon.ico',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info .title { color: #1976d2 }
      `,
    });

    logger.log(`📚 API文档已启用: http://localhost:${port}/docs`);
  }

  // 4. 优雅关闭
  process.on('SIGTERM', async () => {
    logger.log('🔄 收到SIGTERM信号，开始优雅关闭...');
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('🔄 收到SIGINT信号，开始优雅关闭...');
    await app.close();
    process.exit(0);
  });

  // 5. 使用 ConfigService 获取微服务配置
  const microserviceConfig = configService.get('microserviceKit');
  const serviceConfig = microserviceConfig?.services[MICROSERVICE_NAMES.CHARACTER_SERVICE];

  if (serviceConfig) {
    const microserviceOptions = {
      transport: serviceConfig.transport,
      options: serviceConfig.options,
    };

    // 调试日志：显示微服务配置
    logger.log(`🔍 微服务配置调试:`);
    logger.log(`📡 传输方式: ${serviceConfig.transport}`);
    logger.log(`🏠 Redis 主机: ${serviceConfig.options.host}`);
    logger.log(`🔌 Redis 端口: ${serviceConfig.options.port}`);
    logger.log(`🗄️  Redis 数据库: ${serviceConfig.options.db}`);
    logger.log(`🔑 Redis 密码: ${serviceConfig.options.password ? '***' : '未设置'}`);

    // 连接微服务传输层
    app.connectMicroservice(microserviceOptions);

    // 启动所有微服务
    await app.startAllMicroservices();
    logger.log(`🔗 微服务传输层已启动 (${serviceConfig.transport})`);

    // 🔧 为微服务注册全局拦截器
    const cacheInterceptor = app.get(CacheInterceptor);
    app.useGlobalInterceptors(cacheInterceptor); // HTTP应用

    // 获取微服务实例并注册拦截器
    const microservices = app.getMicroservices();
    microservices.forEach(microservice => {
      microservice.useGlobalInterceptors(cacheInterceptor); // 微服务应用
    });

    logger.log(`🔧 已为HTTP和微服务注册全局缓存拦截器`);
  } else {
    logger.error(`❌ 未找到微服务配置: ${MICROSERVICE_NAMES.CHARACTER_SERVICE}`);
    logger.error(`📋 可用配置: ${Object.keys(microserviceConfig?.services || {}).join(', ')}`);
  }

  // 6. 启动HTTP服务
  await app.listen(port, '0.0.0.0');

  logger.log(`🚀 角色服务已启动`);
  logger.log(`📍 HTTP端口: ${port}`);
  logger.log(`🔗 微服务: Redis传输层`);
  logger.log(`🌍 环境: ${environment}`);
  logger.log(`🔗 健康检查: http://localhost:${port}/health`);

  if (environment !== 'production') {
    logger.log(`📚 API文档: http://localhost:${port}/docs`);
  }

  // 7. 服务注册现在由 ServiceRegistryModule.forService() 自动处理
  logger.log(`🚀 Character服务已启动，自动注册功能已启用`);
}

// 启动应用
bootstrap().catch((error) => {
  console.error('❌ 应用启动失败:', error);
  process.exit(1);
});
