import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 配置
import appConfig from './config/app.config';

// 功能模块
import { HealthModule } from '@character/modules/health/health.module';
import { CharacterModule } from '@character/modules/character/character.module';
import { FormationModule } from '@character/modules/formation/formation.module';
import { ItemModule } from '@character/modules/item/item.module';
import { InventoryModule } from '@character/modules/inventory/inventory.module';
import { TacticModule } from '@character/modules/tactic/tactic.module';

// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@app/database/mongodb.config';
import { RedisModule } from '@common/redis';
import { GameConfigModule } from "@app/game-config";
import { MicroserviceKitModule } from '@common/microservice-kit';
import { ServiceMeshModule } from '@libs/service-mesh';
import { MICROSERVICE_NAMES } from '@shared/constants';

@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      load: [appConfig],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`, // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/character/.env.server',                    // 4. 区服配置
        'apps/character/.env.redis',                     // 5. Redis业务配置
        'apps/character/.env.database',                  // 6. 数据库配置
        'apps/character/.env.security',                  // 7. 安全业务配置

        // 环境特定的业务配置（如果存在）
        `apps/character/.env.server.${process.env.NODE_ENV || 'development'}`,
        `apps/character/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/character/.env.database.${process.env.NODE_ENV || 'development'}`,
        `apps/character/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/character/.env.local',                     // 8. 服务本地覆盖
      ],
    }),

    // 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, 'character'),
        ...setupDatabaseEvents('character'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块
    RedisModule.forRootAsync({
      service: 'character',
      serverId: process.env.SERVER_ID || 'server_001', // 从环境变量获取区服ID
    }),

    // 游戏配置模块（核心基础设施）
    GameConfigModule.forRootAsync(),

    // 微服务公共库 - 混合模式（既是服务端又是客户端）
    MicroserviceKitModule.forHybrid(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,  // 作为服务端的服务名
      {
        services: [                               // 作为客户端需要连接的服务
          MICROSERVICE_NAMES.HERO_SERVICE,        // 需要调用球员服务（获取球员信息、计算队伍价值）
          MICROSERVICE_NAMES.ECONOMY_SERVICE,     // 需要调用经济服务（货币操作）
          MICROSERVICE_NAMES.ACTIVITY_SERVICE,    // 需要调用活动服务（任务系统）
        ],
      }
    ),

    // 自动服务注册
    ServiceMeshModule.forServer('character', {
      weight: 1,
      metadata: {
        version: '1.0.0',
        features: ['character-management', 'formation', 'inventory', 'tactic'],
        description: '角色管理服务',
      },
    }),

    // 功能模块
    HealthModule,
    CharacterModule,
    FormationModule,
    ItemModule,
    InventoryModule,
    TacticModule,
  ],
})
export class AppModule {}
