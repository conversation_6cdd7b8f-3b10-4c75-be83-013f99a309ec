/**
 * 测试Payload注入机制
 * 直接调用character微服务验证payload注入是否正常工作
 */

const Redis = require('ioredis');

// Redis配置
const redisConfig = {
  host: '***************',
  port: 6379,
  password: '123456',
  db: 0,
};

async function testPayloadInjection() {
  console.log('🔍 开始测试Payload注入机制');
  console.log('============================================================');

  const redis = new Redis(redisConfig);

  try {
    // 模拟增强后的payload
    const mockEnhancedPayload = {
      // 原始字段
      characterId: 'test_char_123',
      serverId: 'server_001',
      
      // 模拟注入字段
      userId: 'test_user_456',
      wsContext: {
        timestamp: Date.now(),
        routingStrategy: 'normal',
        messageId: 'test_msg_789',
      },
      serverContext: {
        serverId: 'server_001',
        characterId: 'test_char_123',
        serverName: '测试区服',
        serverRegion: 'test-region',
        characterLevel: 25,
        characterName: '测试角色',
      },
    };

    console.log('📋 模拟的增强Payload:');
    console.log(JSON.stringify(mockEnhancedPayload, null, 2));
    console.log('');

    // 测试1：直接调用character.getInfo
    console.log('🧪 测试1：调用character.getInfo');
    console.log('发送消息到Redis队列...');
    
    const testMessage = {
      pattern: 'character.getInfo',
      data: mockEnhancedPayload,
      id: `test_${Date.now()}`,
    };

    // 发送到character服务的Redis队列
    await redis.lpush('character', JSON.stringify(testMessage));
    console.log('✅ 消息已发送到character服务队列');
    console.log('');

    // 等待一下让服务处理
    console.log('⏳ 等待服务处理消息...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试2：验证缓存键生成
    console.log('🧪 测试2：验证缓存键生成');
    
    // 模拟缓存键解析
    const expectedCacheKey = `development:fm:${mockEnhancedPayload.serverId}:character:character:info:${mockEnhancedPayload.characterId}`;
    console.log(`预期缓存键: ${expectedCacheKey}`);
    
    // 检查Redis中是否有相关键
    const keys = await redis.keys('development:fm:server_001:character:*');
    console.log(`Redis中相关键数量: ${keys.length}`);
    if (keys.length > 0) {
      console.log('相关键列表:');
      keys.forEach(key => console.log(`  - ${key}`));
    }
    console.log('');

    // 测试3：验证表达式解析
    console.log('🧪 测试3：验证表达式解析');
    
    const expressions = [
      'payload.characterId',
      'payload.userId',
      'payload.serverContext.serverId',
      'payload.serverContext.characterLevel',
      'payload.wsContext.timestamp',
    ];

    console.log('表达式解析测试:');
    expressions.forEach(expr => {
      const value = evaluateExpression(expr, { payload: mockEnhancedPayload });
      console.log(`  ${expr} = ${value}`);
    });
    console.log('');

    // 测试4：验证条件表达式
    console.log('🧪 测试4：验证条件表达式');
    
    const conditions = [
      'payload.characterId != null',
      'payload.serverContext.characterLevel > 10',
      'payload.serverContext.characterLevel > 30',
      'payload.wsContext.routingStrategy == "normal"',
    ];

    console.log('条件表达式测试:');
    conditions.forEach(condition => {
      const result = evaluateCondition(condition, { payload: mockEnhancedPayload });
      console.log(`  ${condition} = ${result}`);
    });

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  } finally {
    await redis.quit();
  }

  console.log('');
  console.log('============================================================');
  console.log('🎯 Payload注入机制测试完成');
}

// 简单的表达式求值器（模拟ExpressionParser）
function evaluateExpression(expression, context) {
  try {
    if (expression.startsWith('payload.')) {
      const path = expression.substring(8); // 移除'payload.'
      return getNestedValue(context.payload, path);
    }
    return expression;
  } catch (error) {
    return 'ERROR';
  }
}

// 简单的条件求值器
function evaluateCondition(condition, context) {
  try {
    // 替换payload引用
    let evalCondition = condition.replace(/payload\.([a-zA-Z0-9_.]+)/g, (match, path) => {
      const value = getNestedValue(context.payload, path);
      return typeof value === 'string' ? `"${value}"` : value;
    });
    
    // 安全的条件求值（仅用于测试）
    return eval(evalCondition);
  } catch (error) {
    return false;
  }
}

// 获取嵌套属性值
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

// 运行测试
testPayloadInjection().catch(console.error);
