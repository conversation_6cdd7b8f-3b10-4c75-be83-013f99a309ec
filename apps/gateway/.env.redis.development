# ===== 网关服务Redis开发环境配置 =====
# 开发环境特定的Redis配置

# 开发环境Redis连接
REDIS_HOST=***************
REDIS_PORT=6379
REDIS_PASSWORD=123456
REDIS_DB=0

# 开发环境Redis前缀（更短，便于调试）
REDIS_KEY_PREFIX=dev:gateway:

# 开发环境TTL配置（更短，便于测试）
REDIS_GATEWAY_SESSION_TTL=1800        # 30分钟（生产环境1小时）
REDIS_GATEWAY_CACHE_TTL=900           # 15分钟（生产环境30分钟）
REDIS_GATEWAY_ROUTE_CACHE_TTL=60      # 1分钟（生产环境5分钟）

# 开发环境限流配置（更宽松）
REDIS_GATEWAY_RATE_LIMIT_WINDOW=60
REDIS_GATEWAY_RATE_LIMIT_MAX=10000    # 开发环境放宽限制

# 开发环境连接池配置
REDIS_GATEWAY_POOL_MIN=1
REDIS_GATEWAY_POOL_MAX=3

# 开发环境调试配置
REDIS_GATEWAY_DEBUG=true
REDIS_GATEWAY_LOG_COMMANDS=true
REDIS_GATEWAY_METRICS_ENABLED=true
REDIS_GATEWAY_HEALTH_CHECK_INTERVAL=10000  # 10秒检查
