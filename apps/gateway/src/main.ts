import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON><PERSON><PERSON>ip<PERSON>, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IoAdapter } from '@nestjs/platform-socket.io';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

import helmet from 'helmet';
import * as compression from 'compression';

import { AppModule } from './app.module';
import { AllExceptionsFilter } from '@gateway/common/filters/all-exceptions.filter';
import { LoggingInterceptor } from '@gateway/common/interceptors/logging.interceptor';
import { TransformInterceptor } from '@gateway/common/interceptors/transform.interceptor';
import { GatewayExceptionFilter } from '@gateway/common/filters/gateway-exception.filter';
import { RequestLoggingInterceptor } from '@gateway/common/interceptors/request-logging.interceptor';
import { ResponseTransformInterceptor } from '@gateway/common/interceptors/response-transform.interceptor';
import { MetricsService } from './modules/monitoring/services/metrics.service';
import { LoggingService } from '@gateway/infra/logging/logging.service';
import { TracingService } from '@gateway/infra/tracing/tracing.service';
import {MICROSERVICE_NAMES} from "@libs/shared";

async function bootstrap() {
  const logger = new Logger('Gateway');

  // 1. 创建应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 2. 获取服务实例
  const metricsService = app.get(MetricsService);
  const loggingService = app.get(LoggingService);
  const tracingService = app.get(TracingService);

  // 3. 初始化核心服务
  await metricsService.initialize();

  // 4. 安全中间件配置
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        imgSrc: [`'self'`, 'data:', 'apollo-server-landing-page.cdn.apollographql.com'],
        scriptSrc: [`'self'`, `https: 'unsafe-inline'`],
        manifestSrc: [`'self'`, 'apollo-server-landing-page.cdn.apollographql.com'],
        frameSrc: [`'self'`, 'sandbox.embed.apollographql.com'],
      },
    },
  }));

  // 5. 压缩中间件
  app.use(compression());

  // 6. CORS 跨域配置
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    credentials: true,
  });

  // 7. WebSocket 适配器
  app.useWebSocketAdapter(new IoAdapter(app));
  
  // 8. 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,                    // 自动移除非装饰器属性
      forbidNonWhitelisted: true,         // 禁止非白名单属性
      transform: true,                    // 自动转换类型
      transformOptions: {
        enableImplicitConversion: true,   // 启用隐式类型转换
      },
    }),
  );

  // 9. 全局异常过滤器
  app.useGlobalFilters(
    new AllExceptionsFilter(),                                    // 通用异常过滤器
    new GatewayExceptionFilter(loggingService, metricsService),   // 网关专用异常过滤器
  );

  // 10. 全局拦截器
  app.useGlobalInterceptors(
    new LoggingInterceptor(),           // 日志拦截器
    new TransformInterceptor(),         // 数据转换拦截器
    new RequestLoggingInterceptor(),    // 请求日志拦截器
    new ResponseTransformInterceptor(), // 响应转换拦截器
  );
  
  // 11. Swagger API 文档
  if (configService.get('NODE_ENV') !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('足球网关服务')
      .setDescription('足球游戏的统一API网关，提供路由、认证、限流等功能')
      .setVersion('1.0.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-API-Key',
          in: 'header',
          description: 'API Key for service-to-service authentication',
        },
        'API-Key',
      )
      .addTag('认证', '用户认证相关接口')
      .addTag('用户', '用户管理相关接口')
      .addTag('角色', '角色管理相关接口')
      .addTag('球员', '球员管理相关接口')
      .addTag('俱乐部', '俱乐部管理相关接口')
      .addTag('比赛', '比赛管理相关接口')
      .addTag('卡牌', '卡牌管理相关接口')
      .addTag('网关', '网关功能相关接口')
      .addServer(`http://localhost:${configService.get('GATEWAY_PORT', 3000)}`, '本地开发环境')
      .addServer('https://api-dev.yourgame.com', '开发环境')
      .addServer('https://api.yourgame.com', '生产环境')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
      customSiteTitle: '足球网关服务 API',
      customfavIcon: '/favicon.ico',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info .title { color: #1976d2 }
      `,
    });
  }
  

  // 12. 启动HTTP服务器
  const port = configService.get('GATEWAY_PORT', 3000);
  const environment = configService.get('NODE_ENV', 'development');

  await app.listen(port, '0.0.0.0');

  // 13. 启动完成日志
  logger.log(`🚀 网关服务已启动`);
  logger.log(`📍 HTTP端口: ${port}`);
  logger.log(`🌍 环境: ${environment}`);
  logger.log(`🔗 健康检查: http://localhost:${port}/health`);
  logger.log(`📊 监控指标: http://localhost:${port}/metrics`);

  if (environment !== 'production') {
    logger.log(`📚 API文档: http://localhost:${port}/docs`);
  }
}

// 启动应用
bootstrap().catch((error) => {
  console.error('❌ 网关服务启动失败:', error);
  process.exit(1);
});
