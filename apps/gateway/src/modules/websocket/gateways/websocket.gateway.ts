import {
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  SubscribeMessage,
  WebSocketGateway as WSGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import {Server, Socket} from 'socket.io';
import {Logger, UseGuards, UsePipes, ValidationPipe} from '@nestjs/common';
import {ConfigService} from '@nestjs/config';
import {JwtService} from '@nestjs/jwt';
import {WsAuthGuard} from '../guards/ws-auth.guard';
import {
  PublicWsEvent,
  SkipWsRateLimit,
  WsRateLimit,
  WsRateLimitGuard
} from '../guards/ws-rate-limit.guard';
import { AccountToken, CharacterToken } from '../decorators/token-scope.decorator';

// 新增：模块化服务
import { MessageRouterService } from '../services/message-router.service';
import { ServerContextService } from '../context/server-context.service';
import { GlobalBroadcastService } from '../../global-messaging/services/global-broadcast.service';
import {SessionService} from '../services/session.service';
import { RedisLockService, RedisService, RedisPubSubService } from '@common/redis';
import { MicroserviceClientService } from '@common/microservice-kit';
import { MICROSERVICE_NAMES, MicroserviceName } from '@shared/constants';
import {
  isTestFeatureAllowed,
  shouldLogTestAccess,
  shouldAlertOnProductionTestAccess,
  createTestLockKey,
  isProductionEnvironment
} from '@common/redis/config/test-environment.config';

// 临时定义接口，直到 @shared 包可用
interface WSMessage {
  id: string;
  type: MessageType;
  service: string;
  action: string;
  payload: any;
  timestamp: number;
}

interface ServiceResponse {
  success: boolean;
  data?: any;
  error?: string;
}

enum MessageType {
  REQUEST = 'request',
  RESPONSE = 'response',
  ERROR = 'error',
}

// 新的客户端消息格式 - 使用 'service.action' 格式
interface ClientWSMessageDto {
  id: string;
  command: string;  // 格式: 'service.action' 或 'service.module.action'
  payload: any;
}

// 原有的内部消息格式，解析后使用
interface WSMessageDto {
  id: string;
  service: string;
  action: string;
  payload: any;
}

// 增强的 Socket 接口
export interface AuthenticatedSocket extends Socket {
  userId?: string;
  user?: {
    id: string;
    username: string;
    email: string;
    roles: string[];
  };
  authenticated?: boolean;
  userRooms?: Set<string>; // 自定义房间跟踪，避免与 Socket.IO 内置 rooms 冲突
  lastActivity?: Date;
  connectionTime?: Date;
  metadata?: Record<string, any>;
}

// WebSocket 消息接口
export interface WebSocketMessage {
  event: string;
  data: any;
  timestamp: Date;
  messageId: string;
  userId?: string;
  room?: string;
}

// 房间信息接口
export interface RoomInfo {
  id: string;
  name: string;
  type: 'match' | 'chat' | 'lobby' | 'private';
  maxUsers: number;
  currentUsers: number;
  users: string[];
  metadata: Record<string, any>;
  createdAt: Date;
}

@WSGateway({
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    credentials: true,
  },
  namespace: '/',
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
})
export class WebSocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebSocketGateway.name);

  // 新增：连接管理
  private readonly connectedClients = new Map<string, AuthenticatedSocket>();
  private readonly userSockets = new Map<string, Set<string>>();
  private readonly rooms = new Map<string, RoomInfo>();
  private readonly messageQueue = new Map<string, WebSocketMessage[]>();

  constructor(
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
    private readonly sessionService: SessionService,
    private readonly redisLockService: RedisLockService,
    private readonly redisService: RedisService,
    private readonly redisPubSubService: RedisPubSubService,
    // 使用新的微服务公共库客户端
    private readonly microserviceClient: MicroserviceClientService,
    // 新增：模块化服务
    private readonly messageRouterService: MessageRouterService,
    private readonly serverContextService: ServerContextService,
    private readonly globalBroadcastService: GlobalBroadcastService,
  ) {}

  afterInit(server: Server) {
    this.logger.log('WebSocket Gateway initialized');

    // 设置服务器级别的中间件
    server.use(async (socket: AuthenticatedSocket, next) => {
      try {
        await this.authenticateSocket(socket);
        next();
      } catch (error) {
        this.logger.warn(`Socket authentication failed: ${error.message}`);
        next(error);
      }
    });

    // 启动清理任务
    this.startCleanupTasks();

    // 订阅全服消息（异步执行，避免阻塞WebSocket初始化）
    this.subscribeToGlobalMessages().catch(error => {
      this.logger.error('Failed to subscribe to global messages during WebSocket initialization:', error);
    });
  }

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const clientId = client.id;
      const userId = client.userId;

      this.logger.log(`Client connected: ${clientId} (User: ${userId || 'anonymous'})`);

      // 记录连接信息
      client.connectionTime = new Date();
      client.lastActivity = new Date();
      // 注意：client.rooms 是 Socket.IO 的内置属性，不能直接设置
      // 我们使用自定义属性来跟踪用户房间
      client.userRooms = new Set();

      this.connectedClients.set(clientId, client);

      if (userId) {
        if (!this.userSockets.has(userId)) {
          this.userSockets.set(userId, new Set());
        }
        this.userSockets.get(userId)!.add(clientId);

        // Join user to their personal room
        client.join(`user:${userId}`);

        // 发送用户上线事件
        this.broadcastUserStatus(userId, 'online');

        // 发送离线消息
        await this.deliverOfflineMessages(userId, client);
      }

      // 发送连接确认
      client.emit('connected', {
        clientId,
        userId,
        timestamp: new Date(),
        serverInfo: {
          version: '1.0.0',
          features: ['chat', 'match', 'notifications', 'microservices'],
        },
      });

      // 更新统计信息
      this.updateConnectionStats();

    } catch (error) {
      this.logger.error(`Error handling connection: ${error.message}`);
      client.disconnect(true);
    }
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    const clientId = client.id;
    const userId = client.userId;

    this.logger.log(`Client disconnected: ${clientId} (User: ${userId || 'anonymous'})`);

    try {
      // 从房间中移除
      if (client.userRooms) {
        for (const roomId of client.userRooms) {
          await this.leaveRoomInternal(client, roomId);
        }
      }

      // 清理连接记录
      this.connectedClients.delete(clientId);

      if (userId) {
        const userSockets = this.userSockets.get(userId);
        if (userSockets) {
          userSockets.delete(clientId);

          // 如果用户没有其他连接，标记为离线
          if (userSockets.size === 0) {
            this.userSockets.delete(userId);
            this.broadcastUserStatus(userId, 'offline');
          }
        }

        // 清理会话
        await this.sessionService.removeSession(userId, clientId);
      }

      // 更新统计信息
      this.updateConnectionStats();

    } catch (error) {
      this.logger.error(`Error handling disconnect: ${error.message}`);
    }
  }

  @SubscribeMessage('message')
  @UseGuards(WsAuthGuard, WsRateLimitGuard)
  @WsRateLimit({ windowMs: 60000, max: 200 })  // 1分钟内最多200条消息
  @UsePipes(new ValidationPipe({ transform: true }))
  async handleMessage(
    @MessageBody() data: ClientWSMessageDto,
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    this.logger.debug(`🔥 handleMessage called with data: ${JSON.stringify(data)}`);
    try {
      // 解析 command 字段，提取 service 和 action
      const parsedMessage = this.parseClientMessage(data);
      this.logger.debug(`🔍 Parsed message: service=${parsedMessage.service}, action=${parsedMessage.action}`);

      this.logger.debug(`🔍 Getting session for socket: ${client.id}`);
      const session = await this.sessionService.getSessionBySocketId(client.id);
      if (!session) {
        this.logger.debug(`❌ Session not found for socket: ${client.id}`);
        this.sendError(client, data.id, 'Session not found');
        return;
      }

      this.logger.debug(`✅ Session found for user: ${session.userId}`);

      // Update last activity
      client.lastActivity = new Date();
      await this.sessionService.updateLastActivity(session.userId);

      // Route message to appropriate microservice
      this.logger.debug(`🚀 Routing message to service: ${parsedMessage.service}, action: ${parsedMessage.action}`);
      const response = await this.routeMessage(parsedMessage, session.userId, client);
      this.logger.debug(`📨 Got response from routeMessage: ${JSON.stringify(response)}`);

      // Send response back to client
      this.sendResponse(client, data.id, response);

    } catch (error) {
      this.logger.error(`Message handling error: ${error.message}`);
      this.sendError(client, data.id, error.message);
    }
  }



  @SubscribeMessage('ping')
  @PublicWsEvent()  // 公开事件，不需要认证
  @SkipWsRateLimit()  // 跳过限流，ping 消息需要快速响应
  handlePing(@ConnectedSocket() client: AuthenticatedSocket) {
    client.lastActivity = new Date();
    client.emit('pong', { timestamp: Date.now() });
  }

  @SubscribeMessage('heartbeat')
  @PublicWsEvent()  // 公开事件，不需要认证
  @WsRateLimit({ windowMs: 30000, max: 10 })  // 30秒内最多10次心跳
  async handleHeartbeat(@ConnectedSocket() client: AuthenticatedSocket) {
    client.lastActivity = new Date();
    client.emit('heartbeat_ack', { timestamp: new Date() });
  }

  @SubscribeMessage('send_chat_message')
  @CharacterToken()  // 聊天需要角色Token
  @UseGuards(WsAuthGuard, WsRateLimitGuard)
  @WsRateLimit({ windowMs: 60000, max: 50 })  // 1分钟内最多50条消息
  @UsePipes(new ValidationPipe({ transform: true }))
  async handleSendChatMessage(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { roomId?: string; targetUserId?: string; message: string; type?: string },
  ) {
    try {
      const { roomId, targetUserId, message, type = 'text' } = data;

      const messageData: WebSocketMessage = {
        event: 'chat_message',
        data: {
          message,
          type,
          sender: {
            id: client.userId,
            username: client.user?.username,
          },
        },
        timestamp: new Date(),
        messageId: this.generateMessageId(),
        userId: client.userId,
        room: roomId,
      };

      if (roomId) {
        // 房间消息
        this.server.to(roomId).emit('chat_message', messageData);
      } else if (targetUserId) {
        // 私聊消息
        await this.sendPrivateMessage(targetUserId, messageData);
      } else {
        client.emit('error', { message: 'Invalid message target' });
        return;
      }

      // 确认消息已发送
      client.emit('message_sent', {
        messageId: messageData.messageId,
        timestamp: messageData.timestamp,
      });

    } catch (error) {
      this.logger.error(`Error sending chat message: ${error.message}`);
      client.emit('error', { message: 'Failed to send message' });
    }
  }

  @SubscribeMessage('join_room')
  @CharacterToken()  // 需要角色Token，因为房间通常与区服相关
  @UseGuards(WsAuthGuard, WsRateLimitGuard)
  @WsRateLimit({ windowMs: 60000, max: 20 })  // 1分钟内最多加入20个房间
  async handleJoinRoom(
    @MessageBody() data: { room: string; password?: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      const session = await this.sessionService.getSessionBySocketId(client.id);
      if (!session) {
        return;
      }

      // Validate room access using microservice
      const hasAccess = await this.validateRoomAccess(session.userId, data.room);
      if (!hasAccess) {
        this.sendError(client, 'join_room', 'Access denied to room');
        return;
      }

      // 检查房间信息
      const roomInfo = await this.getRoomInfo(data.room);
      if (roomInfo && roomInfo.currentUsers >= roomInfo.maxUsers) {
        client.emit('error', { message: 'Room is full' });
        return;
      }

      // 加入房间
      await this.joinRoomInternal(client, data.room);

      client.emit('room_joined', {
        room: data.room,
        roomInfo: roomInfo ? this.sanitizeRoomInfo(roomInfo) : null,
        timestamp: new Date()
      });

      // 通知房间其他用户
      client.to(data.room).emit('user_joined', {
        userId: client.userId,
        user: client.user,
        timestamp: new Date(),
      });

      this.logger.log(`User ${session.userId} joined room: ${data.room}`);
    } catch (error) {
      this.logger.error(`Join room error: ${error.message}`);
      this.sendError(client, 'join_room', error.message);
    }
  }

  @SubscribeMessage('leave_room')
  @CharacterToken()  // 需要角色Token
  @UseGuards(WsAuthGuard, WsRateLimitGuard)
  @WsRateLimit({ windowMs: 60000, max: 30 })  // 1分钟内最多离开30个房间
  async handleLeaveRoom(
    @MessageBody() data: { room: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    try {
      await this.leaveRoomInternal(client, data.room);

      client.emit('room_left', {
        room: data.room,
        timestamp: new Date()
      });

      // 通知房间其他用户
      client.to(data.room).emit('user_left', {
        userId: client.userId,
        timestamp: new Date(),
      });

      const session = await this.sessionService.getSessionBySocketId(client.id);
      if (session) {
        this.logger.log(`User ${session.userId} left room: ${data.room}`);
      }
    } catch (error) {
      this.logger.error(`Leave room error: ${error.message}`);
    }
  }

  // ==================== Redis分布式锁事件 ====================

  @SubscribeMessage('acquire_lock')
  @PublicWsEvent()  // 公开事件，用于测试
  @WsRateLimit({ windowMs: 60000, max: 50 })  // 1分钟内最多50次锁操作
  async handleAcquireLock(
    @MessageBody() data: { lockKey: string; ttl?: number; clientId: string; reentrant?: boolean },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    // 🔒 环境安全检查
    if (!isTestFeatureAllowed()) {
      if (shouldLogTestAccess()) {
        this.logger.warn(`Test feature access denied: acquire_lock for client ${client.id} in ${process.env.NODE_ENV} environment`);
      }

      if (shouldAlertOnProductionTestAccess() && isProductionEnvironment()) {
        // 这里可以集成告警系统
        this.logger.error(`🚨 SECURITY ALERT: Test lock feature accessed in production by client ${client.id}`);
      }

      client.emit('lock_error', {
        lockKey: data.lockKey,
        clientId: data.clientId,
        error: '测试功能在当前环境中不可用',
        timestamp: new Date()
      });
      return;
    }

    try {
      const { lockKey, ttl = 5000, clientId, reentrant = false } = data;

      // 根据是否需要可重入锁选择不同的方法
      let acquiredLockId: string | null;

      // 创建环境安全的测试锁键
      const safeLockKey = createTestLockKey(lockKey);

      if (reentrant) {
        // 使用可重入锁
        acquiredLockId = await this.redisLockService.acquireReentrantLock(safeLockKey, {
          ttl: Math.ceil(ttl / 1000),
          identifier: clientId, // 使用clientId作为标识符，这样同一客户端可以重复获取
        });
      } else {
        // 使用普通锁
        acquiredLockId = await this.redisLockService.acquireLock(safeLockKey, {
          ttl: Math.ceil(ttl / 1000),
          maxRetries: 1,
          retryDelay: 100,
        });
      }

      if (acquiredLockId) {
        client.emit('lock_acquired', {
          lockKey,
          lockId: acquiredLockId,
          clientId,
          ttl,
          reentrant,
          timestamp: new Date(),
          success: true
        });

        this.logger.log(`${reentrant ? 'Reentrant ' : ''}Lock acquired: ${lockKey} by ${clientId}, lockId: ${acquiredLockId}`);
      } else {
        client.emit('lock_failed', {
          lockKey,
          clientId,
          reason: reentrant ? 'Reentrant lock acquisition failed' : 'Lock already held by another client',
          timestamp: new Date(),
          success: false
        });
      }
    } catch (error) {
      this.logger.error(`Acquire lock error: ${error.message}`);
      client.emit('lock_error', {
        lockKey: data.lockKey,
        clientId: data.clientId,
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  @SubscribeMessage('release_lock')
  @PublicWsEvent()  // 公开事件，用于测试
  @WsRateLimit({ windowMs: 60000, max: 50 })  // 1分钟内最多50次锁操作
  async handleReleaseLock(
    @MessageBody() data: { lockKey: string; lockId: string; clientId: string; reentrant?: boolean },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    // 🔒 环境安全检查
    if (!isTestFeatureAllowed()) {
      if (shouldLogTestAccess()) {
        this.logger.warn(`Test feature access denied: release_lock for client ${client.id} in ${process.env.NODE_ENV} environment`);
      }

      if (shouldAlertOnProductionTestAccess() && isProductionEnvironment()) {
        this.logger.error(`🚨 SECURITY ALERT: Test lock release accessed in production by client ${client.id}`);
      }

      client.emit('lock_error', {
        lockKey: data.lockKey,
        clientId: data.clientId,
        error: '测试功能在当前环境中不可用',
        timestamp: new Date()
      });
      return;
    }

    try {
      const { lockKey, lockId, clientId, reentrant = false } = data;

      // 创建环境安全的测试锁键
      const safeLockKey = createTestLockKey(lockKey);

      // 根据是否是可重入锁选择不同的释放方法
      let lockReleased: boolean;

      if (reentrant) {
        // 释放可重入锁
        lockReleased = await this.redisLockService.releaseReentrantLock(safeLockKey, lockId);
      } else {
        // 释放普通锁
        lockReleased = await this.redisLockService.releaseLock(safeLockKey, lockId);
      }

      if (lockReleased) {
        client.emit('lock_released', {
          lockKey,
          lockId,
          clientId,
          reentrant,
          timestamp: new Date(),
          success: true
        });

        this.logger.log(`${reentrant ? 'Reentrant ' : ''}Lock released: ${lockKey} by ${clientId}, lockId: ${lockId}`);
      } else {
        client.emit('lock_release_failed', {
          lockKey,
          lockId,
          clientId,
          reason: reentrant ? 'Reentrant lock release failed' : 'Lock not found or not owned by client',
          timestamp: new Date(),
          success: false
        });
      }
    } catch (error) {
      this.logger.error(`Release lock error: ${error.message}`);
      client.emit('lock_error', {
        lockKey: data.lockKey,
        clientId: data.clientId,
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  // ==================== 性能测试事件 ====================

  @SubscribeMessage('performance_test')
  @PublicWsEvent()  // 公开事件，用于性能测试
  @WsRateLimit({ windowMs: 60000, max: 200 })  // 1分钟内最多200次性能测试
  async handlePerformanceTest(
    @MessageBody() data: { messageId: number; timestamp: number; data: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    // 🔒 环境安全检查
    if (!isTestFeatureAllowed()) {
      if (shouldLogTestAccess()) {
        this.logger.warn(`Test feature access denied: performance_test for client ${client.id} in ${process.env.NODE_ENV} environment`);
      }

      if (shouldAlertOnProductionTestAccess() && isProductionEnvironment()) {
        this.logger.error(`🚨 SECURITY ALERT: Performance test feature accessed in production by client ${client.id}`);
      }

      client.emit('performance_error', {
        messageId: data.messageId,
        error: '测试功能在当前环境中不可用',
        timestamp: new Date()
      });
      return;
    }

    try {
      const { messageId, timestamp, data: testData } = data;

      // 模拟一些Redis操作以测试性能
      const startTime = Date.now();

      // 执行一些轻量级的Redis操作
      await this.redisService.set(`perf:test:${messageId}`, testData, 60);
      const retrievedData = await this.redisService.get(`perf:test:${messageId}`);

      const processingTime = Date.now() - startTime;

      // 立即响应以测试往返时间
      client.emit('performance_response', {
        messageId,
        timestamp, // 保持原始时间戳用于计算延迟
        data: retrievedData,
        processingTime,
        serverTimestamp: Date.now(),
        success: true
      });

    } catch (error) {
      this.logger.error(`Performance test error: ${error.message}`);
      client.emit('performance_error', {
        messageId: data.messageId,
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  // ==================== Redis发布订阅测试事件 ====================

  @SubscribeMessage('pubsub_test')
  @PublicWsEvent()  // 公开事件，用于发布订阅测试
  @WsRateLimit({ windowMs: 60000, max: 50 })  // 1分钟内最多50次发布订阅测试
  async handlePubSubTest(
    @MessageBody() data: { channel: string; message: any; testId?: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    // 🔒 环境安全检查
    if (!isTestFeatureAllowed()) {
      if (shouldLogTestAccess()) {
        this.logger.warn(`Test feature access denied: pubsub_test for client ${client.id} in ${process.env.NODE_ENV} environment`);
      }

      if (shouldAlertOnProductionTestAccess() && isProductionEnvironment()) {
        this.logger.error(`🚨 SECURITY ALERT: PubSub test feature accessed in production by client ${client.id}`);
      }

      client.emit('pubsub_error', {
        testId: data.testId,
        error: '测试功能在当前环境中不可用',
        timestamp: new Date()
      });
      return;
    }

    try {
      const { channel, message, testId } = data;

      // 通过Redis发布消息
      await this.redisPubSubService.publish(channel, {
        message,
        testId,
        publishedBy: client.id,
        timestamp: Date.now()
      });

      // 确认发布成功
      client.emit('pubsub_published', {
        channel,
        message,
        testId,
        publishedBy: client.id,
        timestamp: Date.now(),
        success: true
      });

      this.logger.log(`PubSub message published to channel ${channel} by client ${client.id}`);

    } catch (error) {
      this.logger.error(`PubSub test error: ${error.message}`);
      client.emit('pubsub_error', {
        testId: data.testId,
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  @SubscribeMessage('pubsub_subscribe')
  @PublicWsEvent()  // 公开事件，用于发布订阅测试
  @WsRateLimit({ windowMs: 60000, max: 20 })  // 1分钟内最多20次订阅
  async handlePubSubSubscribe(
    @MessageBody() data: { channel: string; testId?: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    // 🔒 环境安全检查
    if (!isTestFeatureAllowed()) {
      if (shouldLogTestAccess()) {
        this.logger.warn(`Test feature access denied: pubsub_subscribe for client ${client.id} in ${process.env.NODE_ENV} environment`);
      }

      if (shouldAlertOnProductionTestAccess() && isProductionEnvironment()) {
        this.logger.error(`🚨 SECURITY ALERT: PubSub subscribe test accessed in production by client ${client.id}`);
      }

      client.emit('pubsub_error', {
        testId: data.testId,
        error: '测试功能在当前环境中不可用',
        timestamp: new Date()
      });
      return;
    }

    try {
      const { channel, testId } = data;

      // 使用RedisPubSubService订阅频道
      await this.redisPubSubService.subscribe(channel, (message) => {
        try {
          // 发送接收到的消息给客户端
          client.emit('pubsub_message', {
            channel: message.channel,
            message: message.data,
            receivedBy: client.id,
            timestamp: Date.now(),
            testId,
            messageId: message.messageId
          });

          this.logger.log(`PubSub message received on channel ${channel} by client ${client.id}`);
        } catch (error) {
          this.logger.error(`Failed to process PubSub message: ${error.message}`);
        }
      });

      // 确认订阅成功
      client.emit('pubsub_subscribed', {
        channel,
        testId,
        subscribedBy: client.id,
        timestamp: Date.now(),
        success: true
      });

      // 当客户端断开连接时清理订阅
      client.on('disconnect', () => {
        this.redisPubSubService.unsubscribe(channel);
      });

      this.logger.log(`Client ${client.id} subscribed to PubSub channel ${channel}`);

    } catch (error) {
      this.logger.error(`PubSub subscribe error: ${error.message}`);
      client.emit('pubsub_error', {
        testId: data.testId,
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  // ==================== Redis数据结构测试事件 ====================

  @SubscribeMessage('data_structure_test')
  @PublicWsEvent()  // 公开事件，用于测试
  @WsRateLimit({ windowMs: 60000, max: 100 })  // 1分钟内最多100次数据结构测试
  async handleDataStructureTest(
    @MessageBody() data: { testId: number; type: string; payload: any },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    // 🔒 环境安全检查
    if (!isTestFeatureAllowed()) {
      if (shouldLogTestAccess()) {
        this.logger.warn(`Test feature access denied: data_structure_test for client ${client.id} in ${process.env.NODE_ENV} environment`);
      }

      if (shouldAlertOnProductionTestAccess() && isProductionEnvironment()) {
        this.logger.error(`🚨 SECURITY ALERT: Test data structure feature accessed in production by client ${client.id}`);
      }

      client.emit('data_structure_error', {
        testId: data.testId,
        error: '测试功能在当前环境中不可用',
        timestamp: new Date()
      });
      return;
    }

    try {
      const { testId, type, payload } = data;

      // 模拟Redis数据结构操作（序列化/反序列化测试）
      const processedPayload = await this.simulateRedisDataStructure(type, payload);

      client.emit('data_structure_response', {
        testId,
        type,
        payload: processedPayload,
        originalPayload: payload,
        success: true,
        timestamp: new Date()
      });

      this.logger.log(`Data structure test completed: ${type} (testId: ${testId})`);
    } catch (error) {
      this.logger.error(`Data structure test error: ${error.message}`);
      client.emit('data_structure_error', {
        testId: data.testId,
        type: data.type,
        error: error.message,
        timestamp: new Date()
      });
    }
  }

  // Public methods for other services to send messages
  async sendToUser(userId: string, event: string, data: any) {
    this.server.to(`user:${userId}`).emit(event, data);
  }

  async sendToRoom(room: string, event: string, data: any) {
    this.server.to(room).emit(event, data);
  }

  async broadcastToAll(event: string, data: any) {
    this.server.emit(event, data);
  }

  /**
   * 解析客户端消息，将 'service.action' 格式转换为内部格式
   * 支持格式：
   * - 'auth.verifyToken' -> service: 'auth', action: 'verifyToken'
   * - 'auth.user.login' -> service: 'auth', action: 'user.login'
   */
  private parseClientMessage(clientMessage: ClientWSMessageDto): WSMessageDto {
    const { id, command, payload } = clientMessage;

    // 按第一个点分割，第一部分是service，其余是action
    const parts = command.split('.');
    if (parts.length < 2) {
      throw new Error(`Invalid command format: '${command}'. Expected format: 'service.action' or 'service.module.action'`);
    }

    const service = parts[0];
    const actionPart = parts.slice(1).join('.');

    // 验证服务名是否有效
    const validServices = Object.values(MICROSERVICE_NAMES);
    if (!validServices.includes(service as MicroserviceName)) {
      throw new Error(`Invalid service name: '${service}'. Valid services: ${validServices.join(', ')}`);
    }

    this.logger.debug(`📝 Parsed command '${command}' -> service: '${service}', action: '${actionPart}'`);

    return {
      id,
      service,
      action: actionPart,
      payload
    };
  }

  private async routeMessage(message: WSMessageDto, userId: string, client?: AuthenticatedSocket): Promise<ServiceResponse> {
    const { service, action, payload } = message;
    this.logger.debug(`🔍 routeMessage started: service=${service}, action=${action}`);

    try {
      // 使用新的消息路由服务
      const routeResult = await this.messageRouterService.routeMessage(
        service,
        action,
        payload,
        userId,
        client?.data
      );

      if (!routeResult.success) {
        throw new Error(routeResult.error || 'Message routing failed');
      }

      return {
        success: true,
        data: routeResult.data
      };
    } catch (error) {
      this.logger.error(`❌ routeMessage error: ${error.message}`);
      throw error;
    }
  }









  // ==================== 模拟方法（用于测试） ====================



  /**
   * 使用Redis进行真实的数据结构操作
   */
  private async simulateRedisDataStructure(type: string, payload: any): Promise<any> {
    try {
      const testKey = `test:data_structure:${type}:${Date.now()}`;

      // 使用真正的Redis进行数据存储和检索
      switch (type) {
        case 'string':
          // 字符串类型
          await this.redisService.set(testKey, payload, 60); // 60秒过期
          const stringResult = await this.redisService.get<string>(testKey);
          await this.redisService.del(testKey); // 清理
          return stringResult;

        case 'object':
          // 对象类型
          await this.redisService.set(testKey, payload, 60);
          const objectResult = await this.redisService.get<any>(testKey);
          await this.redisService.del(testKey);
          return objectResult;

        case 'array':
          // 数组类型
          await this.redisService.set(testKey, payload, 60);
          const arrayResult = await this.redisService.get<any[]>(testKey);
          await this.redisService.del(testKey);
          return arrayResult;

        case 'nested':
          // 嵌套对象
          await this.redisService.set(testKey, payload, 60);
          const nestedResult = await this.redisService.get<any>(testKey);
          await this.redisService.del(testKey);
          return nestedResult;

        default:
          // 默认处理
          await this.redisService.set(testKey, payload, 60);
          const defaultResult = await this.redisService.get<any>(testKey);
          await this.redisService.del(testKey);
          return defaultResult;
      }
    } catch (error) {
      this.logger.error(`Redis data structure operation error: ${error.message}`);
      throw error;
    }
  }

  private async validateRoomAccess(userId: string, room: string): Promise<boolean> {
    if (room.startsWith('user:')) {
      return room === `user:${userId}`;
    }

    if (room.startsWith('match:')) {
      const matchId = room.split(':')[1];
      const result = await this.microserviceClient.call(MICROSERVICE_NAMES.MATCH_SERVICE, 'canAccess',
        { userId, matchId }
      );
      return result && result.success;
    }

    if (room.startsWith('club:')) {
      const clubId = room.split(':')[1];
      const result = await this.microserviceClient.call(MICROSERVICE_NAMES.CLUB_SERVICE, 'canAccess',
        { userId, clubId }
      );
      return result && result.success;
    }

    // Default deny
    return false;
  }

  private extractTokenFromSocket(client: Socket): string | null {
    const token = client.handshake.auth?.token || 
                 client.handshake.headers?.authorization?.replace('Bearer ', '');
    return token || null;
  }

  private sendResponse(client: Socket, messageId: string, response: ServiceResponse) {
    const wsResponse: WSMessage = {
      id: messageId,
      type: MessageType.RESPONSE,
      service: 'gateway',
      action: 'response',
      payload: response,
      timestamp: Date.now(),
    };
    
    client.emit('message', wsResponse);
  }

  private sendError(client: AuthenticatedSocket, messageId: string, error: string) {
    const wsError: WSMessage = {
      id: messageId,
      type: MessageType.ERROR,
      service: 'gateway',
      action: 'error',
      payload: { error },
      timestamp: Date.now(),
    };

    client.emit('message', wsError);
  }

  // ==================== 新增功能方法 ====================

  /**
   * 获取WebSocket网关状态
   */
  public getGatewayStatus() {
    return {
      connectedClients: this.connectedClients.size,
      totalRooms: this.rooms.size,
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 检查微服务健康状态
   */
  public async checkMicroservicesHealth(): Promise<Record<string, boolean>> {
    const services = [
      MICROSERVICE_NAMES.AUTH_SERVICE,
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      MICROSERVICE_NAMES.GAME_SERVICE,
      MICROSERVICE_NAMES.CLUB_SERVICE,
      MICROSERVICE_NAMES.MATCH_SERVICE,
      MICROSERVICE_NAMES.CARD_SERVICE
    ];
    const healthStatus: Record<string, boolean> = {};

    for (const service of services) {
      try {
        const response = await this.microserviceClient.call(service, 'health', {});
        healthStatus[service] = response && response.success;
      } catch (error) {
        healthStatus[service] = false;
      }
    }

    return healthStatus;
  }

  private async authenticateSocket(socket: AuthenticatedSocket): Promise<void> {
    const token = socket.handshake.auth?.token || socket.handshake.query?.token;

    if (!token) {
      socket.authenticated = false;
      return; // 允许匿名连接，但功能受限
    }

    try {
      // 先解码Token以确定类型，不验证签名
      const decoded = this.jwtService.decode(token as string) as any;

      if (!decoded || !decoded.scope) {
        throw new Error('Invalid token format');
      }

      let payload: any;

      // 根据Token类型使用对应的密钥验证
      if (decoded.scope === 'account') {
        // 账号Token：使用默认密钥
        payload = this.jwtService.verify(token as string);
      } else if (decoded.scope === 'character') {
        // 角色Token：使用角色Token密钥
        const characterSecret = this.configService.get<string>('gateway.security.characterJwtSecret');
        payload = this.jwtService.verify(token as string, { secret: characterSecret });
      } else {
        throw new Error(`Unsupported token scope: ${decoded.scope}`);
      }

      if (payload) {
        socket.userId = payload.sub;
        socket.user = {
          id: payload.sub,
          username: payload.username,
          email: payload.email,
          roles: payload.roles || [],
        };
        socket.authenticated = true;
        socket.metadata = { tokenScope: payload.scope };

        // 如果是角色Token，附加角色信息
        if (payload.scope === 'character') {
          socket.metadata.characterId = payload.characterId;
          socket.metadata.serverId = payload.serverId;
        }

        // 创建会话
        await this.sessionService.createSession(payload.sub, socket.id);
      }
    } catch (error) {
      throw new Error('Invalid authentication token');
    }
  }

  private async joinRoomInternal(client: AuthenticatedSocket, roomId: string): Promise<void> {
    await client.join(roomId);
    client.userRooms?.add(roomId);

    const room = this.rooms.get(roomId);
    if (room && client.userId) {
      room.currentUsers++;
      room.users.push(client.userId);
    }
  }

  private async leaveRoomInternal(client: AuthenticatedSocket, roomId: string): Promise<void> {
    await client.leave(roomId);
    client.userRooms?.delete(roomId);

    const room = this.rooms.get(roomId);
    if (room && client.userId) {
      room.currentUsers = Math.max(0, room.currentUsers - 1);
      room.users = room.users.filter(id => id !== client.userId);
    }
  }

  private async getRoomInfo(roomId: string): Promise<RoomInfo | null> {
    // 首先检查本地缓存
    let room = this.rooms.get(roomId);

    if (!room) {
      // 如果是游戏相关房间，从微服务获取信息
      if (roomId.startsWith('match:')) {
        const matchId = roomId.split(':')[1];
        try {
          const response = await this.microserviceClient.call(
            MICROSERVICE_NAMES.MATCH_SERVICE,
            'getRoomInfo',
            { matchId }
          );
          if (response && response.success) {
            room = {
              id: roomId,
              name: response.data.name || `Match ${matchId}`,
              type: 'match',
              maxUsers: response.data.maxUsers || 100,
              currentUsers: 0,
              users: [],
              metadata: response.data.metadata || {},
              createdAt: new Date(response.data.createdAt),
            };
            this.rooms.set(roomId, room);
          }
        } catch (error) {
          this.logger.error(`Failed to get match room info: ${error.message}`);
        }
      } else if (roomId.startsWith('club:')) {
        const clubId = roomId.split(':')[1];
        try {
          const response = await this.microserviceClient.call(
            MICROSERVICE_NAMES.CLUB_SERVICE,
            'getRoomInfo',
            { clubId }
          );
          if (response && response.success) {
            room = {
              id: roomId,
              name: response.data.name || `Club ${clubId}`,
              type: 'chat',
              maxUsers: response.data.maxUsers || 50,
              currentUsers: 0,
              users: [],
              metadata: response.data.metadata || {},
              createdAt: new Date(response.data.createdAt),
            };
            this.rooms.set(roomId, room);
          }
        } catch (error) {
          this.logger.error(`Failed to get club room info: ${error.message}`);
        }
      }
    }

    return room || null;
  }

  private async sendPrivateMessage(targetUserId: string, message: WebSocketMessage): Promise<void> {
    const targetSockets = this.userSockets.get(targetUserId);

    if (targetSockets && targetSockets.size > 0) {
      // 用户在线，直接发送
      for (const socketId of targetSockets) {
        const socket = this.connectedClients.get(socketId);
        if (socket) {
          socket.emit('private_message', message);
        }
      }
    } else {
      // 用户离线，存储消息
      await this.storeOfflineMessage(targetUserId, message);
    }
  }

  private async storeOfflineMessage(userId: string, message: WebSocketMessage): Promise<void> {
    if (!this.messageQueue.has(userId)) {
      this.messageQueue.set(userId, []);
    }

    const userMessages = this.messageQueue.get(userId)!;
    userMessages.push(message);

    // 限制离线消息数量
    if (userMessages.length > 100) {
      userMessages.shift();
    }
  }

  private async deliverOfflineMessages(userId: string, client: AuthenticatedSocket): Promise<void> {
    const messages = this.messageQueue.get(userId);

    if (messages && messages.length > 0) {
      client.emit('offline_messages', {
        messages,
        count: messages.length,
      });

      // 清除已发送的离线消息
      this.messageQueue.delete(userId);
    }
  }

  private broadcastUserStatus(userId: string, status: 'online' | 'offline'): void {
    this.server.emit('user_status', {
      userId,
      status,
      timestamp: new Date(),
    });
  }

  private sanitizeRoomInfo(room: RoomInfo): Partial<RoomInfo> {
    return {
      id: room.id,
      name: room.name,
      type: room.type,
      maxUsers: room.maxUsers,
      currentUsers: room.currentUsers,
    };
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateConnectionStats(): void {
    const stats = {
      totalConnections: this.connectedClients.size,
      authenticatedConnections: Array.from(this.connectedClients.values()).filter(c => c.authenticated).length,
      totalRooms: this.rooms.size,
      timestamp: new Date(),
    };

    // 这里可以发送统计信息到监控系统
    this.logger.debug('Connection stats updated', stats);
  }

  private startCleanupTasks(): void {
    // 每5分钟清理不活跃的连接
    setInterval(() => {
      this.cleanupInactiveConnections();
    }, 5 * 60 * 1000);

    // 每小时清理空房间
    setInterval(() => {
      this.cleanupEmptyRooms();
    }, 60 * 60 * 1000);
  }

  private cleanupInactiveConnections(): void {
    const now = new Date();
    const timeout = 10 * 60 * 1000; // 10分钟超时

    for (const [clientId, client] of this.connectedClients) {
      if (client.lastActivity && (now.getTime() - client.lastActivity.getTime()) > timeout) {
        this.logger.log(`Disconnecting inactive client: ${clientId}`);
        client.disconnect(true);
      }
    }
  }

  private cleanupEmptyRooms(): void {
    for (const [roomId, room] of this.rooms) {
      if (room.currentUsers === 0) {
        this.rooms.delete(roomId);
        this.logger.log(`Cleaned up empty room: ${roomId}`);
      }
    }
  }

  // ==================== 全服消息处理 ====================

  /**
   * 订阅全服消息
   */
  private async subscribeToGlobalMessages(): Promise<void> {
    const maxRetries = 10;
    const retryDelay = 2000; // 2秒

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // 订阅全服广播频道
        await this.redisPubSubService.subscribe('global_messages:broadcast', (message) => {
          this.handleGlobalBroadcast(JSON.parse(message.data || message.toString()));
        });

        // 订阅系统公告频道
        await this.redisPubSubService.subscribe('global_messages:announcement', (message) => {
          this.handleSystemAnnouncement(JSON.parse(message.data || message.toString()));
        });

        // 订阅紧急通知频道
        await this.redisPubSubService.subscribe('global_messages:emergency', (message) => {
          this.handleEmergencyNotification(JSON.parse(message.data || message.toString()));
        });

        this.logger.log('✅ Successfully subscribed to global message channels');
        return; // 成功后退出
      } catch (error) {
        this.logger.warn(`⚠️ Failed to subscribe to global messages (attempt ${attempt}/${maxRetries}): ${error.message}`);

        if (attempt === maxRetries) {
          this.logger.error('❌ Failed to subscribe to global messages after all retries');
          throw error; // 最后一次失败时抛出错误
        }

        // 等待后重试
        this.logger.debug(`Retrying in ${retryDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      }
    }
  }

  /**
   * 处理全服广播消息
   */
  private async handleGlobalBroadcast(message: any): Promise<void> {
    this.logger.log(`📢 Handling global broadcast: ${message.type}`);

    try {
      // 根据消息目标筛选用户
      const targetUsers = await this.resolveMessageTargets(message.target);

      // 向目标用户广播消息
      for (const userId of targetUsers) {
        const userSockets = this.userSockets.get(userId);
        if (userSockets) {
          for (const socketId of userSockets) {
            const socket = this.connectedClients.get(socketId);
            if (socket) {
              socket.emit('global.message', {
                id: message.id,
                type: message.type,
                title: message.title,
                content: message.content,
                priority: message.priority,
                displayType: message.displayType || 'notification',
                timestamp: new Date(),
              });
            }
          }
        }
      }

      this.logger.log(`📢 Global broadcast sent to ${targetUsers.length} users`);
    } catch (error) {
      this.logger.error('❌ Failed to handle global broadcast:', error);
    }
  }

  /**
   * 处理系统公告
   */
  private async handleSystemAnnouncement(message: any): Promise<void> {
    this.logger.log(`📋 Handling system announcement: ${message.title}`);

    // 系统公告通常需要全服广播
    this.server.emit('global.announcement', {
      id: message.id,
      title: message.title,
      content: message.content,
      displayType: message.displayType || 'popup',
      autoClose: message.autoClose || false,
      closeDelay: message.closeDelay || 10,
      actionButton: message.actionButton,
      timestamp: new Date(),
    });

    this.logger.log('📋 System announcement broadcasted to all users');
  }

  /**
   * 处理紧急通知
   */
  private async handleEmergencyNotification(message: any): Promise<void> {
    this.logger.warn(`🚨 Handling emergency notification: ${message.alertLevel}`);

    // 紧急通知强制发送给所有在线用户
    this.server.emit('global.emergency', {
      id: message.id,
      alertLevel: message.alertLevel,
      title: message.title,
      content: message.content,
      action: message.action,
      countdown: message.countdown,
      forceDisplay: message.forceDisplay || true,
      blockGameplay: message.blockGameplay || false,
      timestamp: new Date(),
    });

    // 如果需要强制登出，执行相应操作
    if (message.action === 'logout') {
      setTimeout(() => {
        this.server.emit('global.force_logout', {
          reason: message.content,
          countdown: message.countdown || 60,
        });
      }, 1000);
    }

    this.logger.warn('🚨 Emergency notification sent to all users');
  }

  /**
   * 解析消息目标用户
   */
  private async resolveMessageTargets(target: any): Promise<string[]> {
    if (!target) {
      return [];
    }

    switch (target.scope) {
      case 'global':
        // 返回所有在线用户
        return Array.from(this.userSockets.keys());

      case 'servers':
        // 返回指定区服的用户
        return this.getUsersByServers(target.servers || []);

      case 'users':
        // 返回指定用户列表
        return target.users || [];

      case 'conditions':
        // 根据条件筛选用户
        return await this.getUsersByConditions(target.conditions || []);

      default:
        return [];
    }
  }

  /**
   * 根据区服获取用户列表
   */
  private getUsersByServers(serverIds: string[]): string[] {
    const targetUsers: string[] = [];

    for (const [userId, socketIds] of this.userSockets) {
      for (const socketId of socketIds) {
        const socket = this.connectedClients.get(socketId);
        if (socket?.data?.character?.serverId && serverIds.includes(socket.data.character.serverId)) {
          targetUsers.push(userId);
          break; // 每个用户只添加一次
        }
      }
    }

    return targetUsers;
  }

  /**
   * 根据条件筛选用户
   */
  private async getUsersByConditions(conditions: any[]): Promise<string[]> {
    // 这里可以实现复杂的用户筛选逻辑
    // 例如根据等级、VIP状态、最后登录时间等条件筛选
    // 暂时返回所有在线用户
    return Array.from(this.userSockets.keys());
  }

  /**
   * 添加全服消息处理的WebSocket事件
   */
  @SubscribeMessage('global.message.ack')
  async handleGlobalMessageAck(
    @MessageBody() data: { messageId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ): Promise<void> {
    const userId = client.data.user?.id;
    if (!userId) {
      return;
    }

    this.logger.debug(`📨 Global message acknowledged: ${data.messageId} by user ${userId}`);

    // 记录消息确认状态（可以存储到数据库或Redis）
    try {
      await this.redisService.set(
        `global_message_ack:${data.messageId}:${userId}`,
        JSON.stringify({
          messageId: data.messageId,
          userId,
          acknowledgedAt: new Date(),
        }),
        3600 * 24 * 7, // 保存7天
        'global'
      );
    } catch (error) {
      this.logger.error('Failed to record message acknowledgment:', error);
    }
  }
}
