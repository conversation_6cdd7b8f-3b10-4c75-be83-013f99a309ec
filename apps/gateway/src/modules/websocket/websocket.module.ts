import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { RedisModule } from '@common/redis';


// JWT共享模块
import { JwtSharedModule } from '@gateway/infra/jwt/jwt-shared.module';

// 基础功能模块
import { RateLimitingModule } from '../rate-limiting/rate-limit.module';

// 认证模块
import { GatewayAuthModule } from '../gateway-auth/gateway-auth.module';

// 全局消息模块
import { GlobalMessagingModule } from '../global-messaging/global-messaging.module';

// WebSocket网关
import { WebSocketGateway } from './gateways/websocket.gateway';

// 服务
import { SessionService } from './services/session.service';
import { MessageRouterService } from './services/message-router.service';

// 守卫
import { WsAuthGuard } from './guards/ws-auth.guard';
import { WsRateLimitGuard } from './guards/ws-rate-limit.guard';

// 上下文服务
import { ServerContextService } from './context/server-context.service';
import { PayloadEnhancerService } from './context/payload-enhancer.service';

/**
 * WebSocket功能模块
 *
 * 职责：
 * - 提供实时WebSocket通信
 * - 管理用户会话和连接
 * - 处理消息路由和分发
 * - 集成认证和限流保护
 *
 * 依赖：
 * - SharedModule：JWT服务、微服务通信
 * - RateLimitingModule：WebSocket限流
 * - GatewayAuthModule：WebSocket认证
 * - GlobalMessagingModule：全局消息服务
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,

    JwtSharedModule,
    RateLimitingModule,
    GatewayAuthModule,
    GlobalMessagingModule,
  ],
  providers: [
    // WebSocket网关
    WebSocketGateway,

    // 服务
    SessionService,
    MessageRouterService,

    // 守卫
    WsAuthGuard,
    WsRateLimitGuard,

    // 上下文服务
    ServerContextService,
    PayloadEnhancerService,
  ],
  exports: [
    WebSocketGateway,
    SessionService,
    MessageRouterService,
  ],
})
export class WebSocketModule {}
