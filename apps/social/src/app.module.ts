import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 配置
import appConfig from './config/app.config';

// 功能模块
import { HealthModule } from '@social/modules/health/health.module';
import { FriendModule } from '@social/modules/friend/friend.module';
import { MailModule } from '@social/modules/mail/mail.module';
import { GuildModule } from '@social/modules/guild/guild.module';
import { ChatModule } from '@social/modules/chat/chat.module';

// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@app/database/mongodb.config';
import { RedisModule } from '@common/redis';
import { GameConfigModule } from '@app/game-config';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';

@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      load: [appConfig],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`,  // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/social/.env.server',                    // 4. 区服配置
        'apps/social/.env.redis',                     // 5. Redis业务配置
        'apps/social/.env.database',                  // 6. 数据库配置
        'apps/social/.env.security',                  // 7. 安全业务配置

        // 环境特定的业务配置（如果存在）
        `apps/social/.env.server.${process.env.NODE_ENV || 'development'}`,
        `apps/social/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/social/.env.database.${process.env.NODE_ENV || 'development'}`,
        `apps/social/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/social/.env.local',                     // 8. 服务本地覆盖
      ],
    }),

    // 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, 'social'),
        ...setupDatabaseEvents('social'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块
    RedisModule.forRootAsync({
      service: 'social',
      serverId: 'server_001',
    }),

    // 游戏配置模块（核心基础设施）
    GameConfigModule.forRootAsync(),

    // 微服务公共库 - 服务端模式
    MicroserviceKitModule.forServer(MICROSERVICE_NAMES.SOCIAL_SERVICE),

    // 功能模块
    HealthModule,
    FriendModule,
    MailModule,
    GuildModule,
    ChatModule,
  ],
})
export class AppModule {}
