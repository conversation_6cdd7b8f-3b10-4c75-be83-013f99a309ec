import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { MICROSERVICE_NAMES } from '@shared/constants';
import { AppModule } from './app.module';

async function bootstrap() {
  // 1. 创建应用实例
  const app = await NestFactory.create(AppModule);

  const configService = app.get(ConfigService);
  const logger = new Logger('HeroService');

  // 2. 获取配置
  const port = configService.get('HERO_PORT', 3007);
  const environment = configService.get('NODE_ENV', 'development');

  // 3. Swagger API 文档
  if (environment !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('足球球员服务')
      .setDescription('提供球员管理、技能培养、训练系统和球探功能的微服务API')
      .setVersion('1.0.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addApiKey(
        {
          type: 'apiKey',
          name: 'X-API-Key',
          in: 'header',
          description: 'API Key for service-to-service authentication',
        },
        'API-Key',
      )
      .addTag('球员', '球员管理相关接口')
      .addTag('技能', '技能系统相关接口')
      .addTag('培养', '球员培养相关接口')
      .addTag('训练', '训练系统相关接口')
      .addTag('球探', '球探系统相关接口')
      .addTag('生涯', '球员生涯相关接口')
      .addTag('场地', '训练场地相关接口')
      .addTag('健康检查', '服务健康状态接口')
      .addServer(`http://localhost:${port}`, '本地开发环境')
      .addServer('https://api-dev.yourgame.com', '开发环境')
      .addServer('https://api.yourgame.com', '生产环境')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
      customSiteTitle: '足球球员服务 API',
      customfavIcon: '/favicon.ico',
      customCss: `
        .swagger-ui .topbar { display: none }
        .swagger-ui .info .title { color: #1976d2 }
      `,
    });

    logger.log(`📚 API文档已启用: http://localhost:${port}/docs`);
  }

  // 4. 优雅关闭
  process.on('SIGTERM', async () => {
    logger.log('🔄 收到SIGTERM信号，开始优雅关闭...');
    await app.close();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    logger.log('🔄 收到SIGINT信号，开始优雅关闭...');
    await app.close();
    process.exit(0);
  });

  // 5. 使用 ConfigService 获取微服务配置
  const microserviceConfig = configService.get('microserviceKit');
  const serviceConfig = microserviceConfig?.services[MICROSERVICE_NAMES.HERO_SERVICE];

  if (serviceConfig) {
    const microserviceOptions = {
      transport: serviceConfig.transport,
      options: serviceConfig.options,
    };

    // 调试日志：显示微服务配置
    logger.log(`🔍 微服务配置调试:`);
    logger.log(`📡 传输方式: ${serviceConfig.transport}`);
    logger.log(`🏠 Redis 主机: ${serviceConfig.options.host}`);
    logger.log(`🔌 Redis 端口: ${serviceConfig.options.port}`);
    logger.log(`🗄️  Redis 数据库: ${serviceConfig.options.db}`);
    logger.log(`🔑 Redis 密码: ${serviceConfig.options.password ? '***' : '未设置'}`);

    // 连接微服务传输层
    app.connectMicroservice(microserviceOptions);

    // 启动所有微服务
    await app.startAllMicroservices();
    logger.log(`🔗 微服务传输层已启动 (${serviceConfig.transport})`);
  } else {
    logger.error(`❌ 未找到微服务配置: ${MICROSERVICE_NAMES.HERO_SERVICE}`);
    logger.error(`📋 可用配置: ${Object.keys(microserviceConfig?.services || {}).join(', ')}`);
  }

  // 6. 启动HTTP服务
  await app.listen(port, '0.0.0.0');

  logger.log(`🚀 球员服务已启动`);
  logger.log(`📍 HTTP端口: ${port}`);
  logger.log(`🔗 微服务: Redis传输层`);
  logger.log(`🌍 环境: ${environment}`);
  logger.log(`🔗 健康检查: http://localhost:${port}/health`);

  if (environment !== 'production') {
    logger.log(`📚 API文档: http://localhost:${port}/docs`);
  }
}

// 启动应用
bootstrap().catch((error) => {
  console.error('❌ 应用启动失败:', error);
  process.exit(1);
});

