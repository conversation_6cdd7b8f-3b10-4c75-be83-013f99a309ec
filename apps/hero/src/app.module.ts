import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 配置
import appConfig from './config/app.config';

// 功能模块
import { HealthModule } from './modules/health/health.module';
import { HeroModule } from './modules/hero/hero.module';
import { SkillModule } from './modules/skill/skill.module';
import { CultivationModule } from './modules/cultivation/cultivation.module';
import { ScoutModule } from './modules/scout/scout.module';
import { CareerModule } from './modules/career/career.module';
import { TrainingModule } from './modules/training/training.module';
import { GroundModule } from './modules/ground/ground.module';


// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@app/database/mongodb.config';
import { RedisModule } from '@common/redis';
import { GameConfigModule } from '@app/game-config';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';

@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      load: [appConfig],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`,  // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/match/.env.server',                    // 4. 区服配置
        'apps/match/.env.redis',                     // 5. Redis业务配置
        'apps/match/.env.database',                  // 6. 数据库配置
        'apps/match/.env.security',                  // 7. 安全业务配置

        // 环境特定的业务配置（如果存在）
        `apps/match/.env.server.${process.env.NODE_ENV || 'development'}`,
        `apps/match/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/match/.env.database.${process.env.NODE_ENV || 'development'}`,
        `apps/match/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/match/.env.local',                     // 8. 服务本地覆盖
      ],
    }),

    // 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, 'hero'),
        ...setupDatabaseEvents('hero'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块
    RedisModule.forRootAsync({
      service: 'hero',
      serverId: 'server_001',
    }),

    // 游戏配置模块（核心基础设施）
    GameConfigModule.forRootAsync(),

    // 微服务公共库 - 混合模式（既是服务端又是客户端）
    MicroserviceKitModule.forHybrid(
      MICROSERVICE_NAMES.HERO_SERVICE,  // 作为服务端的服务名
      {
        services: [                              // 作为客户端需要连接的服务
          MICROSERVICE_NAMES.CHARACTER_SERVICE,  // 需要调用角色服务（金币检查、阵容信息）
          MICROSERVICE_NAMES.ECONOMY_SERVICE,    // 需要调用经济服务（市场交易）
          MICROSERVICE_NAMES.ACTIVITY_SERVICE,   // 需要调用活动服务（任务进度）
        ],
      }
    ),

    // 功能模块
    HealthModule,
    HeroModule,
    SkillModule,

    // 业务模块（基于old项目功能）
    CultivationModule,  // 球员养成系统
    ScoutModule,        // 球探系统
    CareerModule,       // 生涯管理系统
    TrainingModule,     // 训练系统
    GroundModule,       // 场地训练系统

  ],
})
export class AppModule {}
