# Character服务区服配置
# ========================================

# 区服基础配置
SERVER_ID=server_001
SERVER_NAME=新手村
SERVER_STATUS=active
SERVER_REGION=cn-east-1

# 服务实例配置
INSTANCE_ID=character-server_001-1
INSTANCE_WEIGHT=1
INSTANCE_MAX_CONNECTIONS=1000

# 区服感知服务注册配置
SERVICE_REGISTRY_ENABLED=true
SERVICE_REGISTRY_HEALTH_CHECK_INTERVAL=30000
SERVICE_REGISTRY_INSTANCE_TIMEOUT=90000

# 区服数据隔离配置
DATA_ISOLATION_ENABLED=true
CROSS_SERVER_ENABLED=false

# 区服容量配置
MAX_CHARACTERS_PER_SERVER=10000
MAX_CONCURRENT_USERS=1000

# 区服维护配置
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=服务器维护中，请稍后再试

# 区服合并配置
MERGE_ENABLED=false
MERGE_TARGET_SERVER=

# 区服监控配置
MONITORING_ENABLED=true
METRICS_COLLECTION_INTERVAL=60000
