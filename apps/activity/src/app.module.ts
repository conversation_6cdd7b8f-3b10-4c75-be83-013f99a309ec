import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

// 配置
import appConfig from './config/app.config';

// 功能模块
import { HealthModule } from '@activity/modules/health/health.module';
import { TaskModule } from '@activity/modules/task/task.module';
import { EventModule } from '@activity/modules/event/event.module';
import { SignModule } from '@activity/modules/sign/sign.module';
import { GuideModule } from '@activity/modules/guide/guide.module';
import { EnergyModule } from '@activity/modules/energy/energy.module';
import { HonorModule } from '@activity/modules/honor/honor.module';

// 基础设施模块
import { createMongoConfig, setupDatabaseEvents } from '@app/database/mongodb.config';
import { RedisModule } from '@common/redis';
import { GameConfigModule } from '@app/game-config';
import { MicroserviceKitModule } from '@common/microservice-kit';
import { MICROSERVICE_NAMES } from '@shared/constants';
import microserviceKitConfig from "@common/microservice-kit/config/default.config";

@Module({
  imports: [
    // 全局配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      load: [appConfig, microserviceKitConfig],
      envFilePath: [
        '.env',                                           // 1. 基础配置
        `.env.${process.env.NODE_ENV || 'development'}`,  // 2. 环境特定配置
        '.env.local',                                     // 3. 本地覆盖

        // 业务模块特定配置
        'apps/activity/.env.server',                    // 4. 区服配置
        'apps/activity/.env.redis',                     // 5. Redis业务配置
        'apps/activity/.env.database',                  // 6. 数据库配置
        'apps/activity/.env.security',                  // 7. 安全业务配置

        // 环境特定的业务配置（如果存在）
        `apps/activity/.env.server.${process.env.NODE_ENV || 'development'}`,
        `apps/activity/.env.redis.${process.env.NODE_ENV || 'development'}`,
        `apps/activity/.env.database.${process.env.NODE_ENV || 'development'}`,
        `apps/activity/.env.security.${process.env.NODE_ENV || 'development'}`,

        // 最终覆盖
        'apps/activity/.env.local',                     // 8. 服务本地覆盖
      ],
    }),

    // 数据库模块
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        ...createMongoConfig(configService, 'activity'),
        ...setupDatabaseEvents('activity'),
      }),
      inject: [ConfigService],
    }),

    // Redis模块
    RedisModule.forRootAsync({
      service: 'activity',
      serverId: 'server_001',
    }),

    // 游戏配置模块（核心基础设施）
    GameConfigModule.forRootAsync(),

    // 微服务公共库 - 混合模式（活动抽奖需要调用多个服务）
    MicroserviceKitModule.forHybrid(
      MICROSERVICE_NAMES.ACTIVITY_SERVICE,  // 作为服务端的服务名
      {
        services: [                               // 作为客户端需要连接的服务
          MICROSERVICE_NAMES.CHARACTER_SERVICE,   // 需要调用角色服务（货币检查和扣除）
          MICROSERVICE_NAMES.ECONOMY_SERVICE,     // 需要调用经济服务（物品奖励发放）
          MICROSERVICE_NAMES.HERO_SERVICE,        // 需要调用球员服务（球员奖励发放）
        ],
      }
    ),

    // 功能模块
    HealthModule,
    TaskModule,
    EventModule,
    SignModule,
    GuideModule,
    EnergyModule,
    HonorModule,
  ],
})
export class AppModule {}
