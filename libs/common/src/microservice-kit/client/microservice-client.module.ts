import { Module, DynamicModule, Global } from '@nestjs/common';
import { ClientsModule } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { MicroserviceKitConfig } from '../config/microservice.config';
import { MicroserviceClientService } from './microservice-client.service';
import { ConnectionPoolService } from './connection-pool.service';
import { ContextExtractorService } from '../utils/context-extractor.service';
import { LoadBalancerService } from '../utils/load-balancer.service';
import { MicroserviceName } from '@shared/constants';

/**
 * 微服务客户端模块
 * 自动为指定的服务创建 ClientProxy 实例
 */
@Global()
@Module({})
export class MicroserviceClientModule {

  /**
   * 创建微服务客户端模块（支持分区分服）
   * @param options 选项，包含要连接的服务列表和功能开关
   */
  static forRoot(options?: {
    services?: MicroserviceName[];
    enableServerAware?: boolean;
    enableTraditionalFallback?: boolean;
  }): DynamicModule {
    const providers: any[] = [
      {
        provide: 'MICROSERVICE_CONFIG',
        useFactory: (configService: ConfigService) => {
          return configService.get<MicroserviceKitConfig>('microserviceKit');
        },
        inject: [ConfigService],
      },
      {
        provide: 'CONNECTED_SERVICES',
        useFactory: (configService: ConfigService) => {
          const config = configService.get<MicroserviceKitConfig>('microserviceKit');
          return options?.services || Object.keys(config.services) as MicroserviceName[];
        },
        inject: [ConfigService],
      },
      // 🚀 新增：功能开关配置
      {
        provide: 'MICROSERVICE_CLIENT_OPTIONS',
        useValue: {
          enableServerAware: options?.enableServerAware ?? true,
          enableTraditionalFallback: options?.enableTraditionalFallback ?? false,
        },
      },
      MicroserviceClientService,
      // 🚀 精简版：只保留核心服务
      ConnectionPoolService,
      ContextExtractorService,
      // 🗑️ LoadBalancerService 已删除 - 功能转移到ServiceMesh
    ];

    return {
      module: MicroserviceClientModule,
      imports: [],
      providers,
      exports: [
        MicroserviceClientService,
        'MICROSERVICE_CONFIG',
        'CONNECTED_SERVICES',
      ],
    };
  }
}
