/**
 * 🚀 MicroserviceKit 精简版导出
 *
 * 新架构职责：
 * - 专注微服务调用功能
 * - 服务注册功能已转移到ServiceMesh
 */

// 🚀 核心客户端模块导出
export * from './client/microservice-client.module';
export * from './client/microservice-client.service';
export * from './client/connection-pool.service';

// 🔧 工具模块导出
export * from './utils/context-extractor.service';
export * from './utils/load-balancer.service';

// ⚙️ 配置导出
export * from './config';

// 🛠️ 工具函数导出（仅保留优雅关闭功能）
export * from './utils/microservice-bootstrap';

// 📦 主模块导出
export * from './microservice-kit.module';

// 🎯 便捷的默认导出
export { MicroserviceKitModule as default } from './microservice-kit.module';

// 🗑️ 已删除的导出：
// - server/microservice-server.module (功能转移到ServiceMesh)
// - server/microservice-server.service (功能转移到ServiceMesh)
