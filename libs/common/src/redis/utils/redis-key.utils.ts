/**
 * Redis键构建工具类
 * 实现Redis前缀架构v3.0的键名构建逻辑
 */

import { DataType, RedisKeyOptions, RedisPrefixConfig } from '../types/redis.types';

export class RedisKeyUtils {
  // 静态配置缓存，避免重复读取环境变量
  private static _currentServerId: string | null = null;
  private static _serviceContext: string | null = null;

  /**
   * 构建带数据类型的完整键名
   *
   * 🎯 优化设计：global和cross数据去掉服务前缀，实现真正的数据共享
   * - 不传dataType：默认为server类型，使用当前区服
   * - global/cross类型：去掉服务前缀，支持跨服务共享
   * - server类型：保留服务前缀，维护服务隔离
   *
   * @param basePrefix 基础前缀 {环境}:{项目}:
   * @param key 业务键名
   * @param options 键构建选项
   * @returns 完整的Redis键名
   *
   * @example
   * // 全局共享数据
   * buildDataTypeKey('dev:fm:', 'config:Hero:all', { dataType: 'global' })
   * // 返回: 'dev:fm:global:config:Hero:all'
   *
   * // 服务特定的全局数据（在具体键中体现服务名）
   * buildDataTypeKey('dev:fm:', 'auth:login:stats', { dataType: 'global' })
   * // 返回: 'dev:fm:global:auth:login:stats'
   *
   * // 跨服共享数据
   * buildDataTypeKey('dev:fm:', 'ranking:global:fans', { dataType: 'cross' })
   * // 返回: 'dev:fm:cross:ranking:global:fans'
   *
   * // 服务特定的跨服数据（在具体键中体现服务名）
   * buildDataTypeKey('dev:fm:', 'match:pvp:queue', { dataType: 'cross' })
   * // 返回: 'dev:fm:cross:match:pvp:queue'
   */
  static buildDataTypeKey(
    basePrefix: string,
    key: string,
    options: RedisKeyOptions = {}
  ): string {
    // 🎯 核心简化：默认为server类型（当前区服缓存）
    const { dataType = 'server', serverId, serviceContext } = options;

    // 移除末尾冒号
    const cleanBasePrefix = basePrefix.replace(/:$/, '');
    const service = serviceContext || RedisKeyUtils.getServiceContext();

    switch (dataType) {
      case 'global':
        // 🔥 优化：去掉服务前缀，实现真正的全局共享
        // 需要服务隔离的数据在{具体键}层面添加服务名，如 'auth:login:stats'
        return `${cleanBasePrefix}:global:${key}`;
      case 'cross':
        // 🔥 优化：去掉服务前缀，实现真正的跨服共享
        // 需要服务隔离的数据在{具体键}层面添加服务名，如 'match:pvp:queue'
        return `${cleanBasePrefix}:cross:${key}`;
      case 'server':
        // 🎯 保持：区服数据保留服务前缀，维护服务隔离
        const targetServerId = serverId || RedisKeyUtils.getCurrentServerId();
        return `${cleanBasePrefix}:server:${targetServerId}:${service}:${key}`;
      default:
        // 兜底：默认server类型
        const defaultServerId = serverId || RedisKeyUtils.getCurrentServerId();
        return `${cleanBasePrefix}:server:${defaultServerId}:${service}:${key}`;
    }
  }

  /**
   * 获取当前区服ID
   * 优先级：运行时设置 > 系统环境变量 > .env文件 > 默认值
   */
  static getCurrentServerId(): string {
    if (RedisKeyUtils._currentServerId !== null) {
      return RedisKeyUtils._currentServerId;
    }

    // 优先级1: 系统环境变量（启动时设置，优先级最高）
    if (process.env.SERVER_ID) {
      RedisKeyUtils._currentServerId = process.env.SERVER_ID;
      return RedisKeyUtils._currentServerId;
    }

    // 优先级2: 从NODE_ENV推断（开发环境的便利性）
    if (process.env.NODE_ENV === 'development') {
      RedisKeyUtils._currentServerId = 'server_001';
      return RedisKeyUtils._currentServerId;
    }

    // 优先级3: 默认值
    RedisKeyUtils._currentServerId = 'server_001';
    return RedisKeyUtils._currentServerId;
  }

  /**
   * 获取服务上下文
   */
  static getServiceContext(): string {
    if (RedisKeyUtils._serviceContext !== null) {
      return RedisKeyUtils._serviceContext;
    }

    RedisKeyUtils._serviceContext = process.env.MICROSERVICE_NAME || 'default';
    return RedisKeyUtils._serviceContext;
  }

  /**
   * 运行时设置当前区服ID（用于动态切换或测试）
   */
  static setCurrentServerId(serverId: string): void {
    RedisKeyUtils._currentServerId = serverId;
  }

  /**
   * 运行时设置服务上下文（用于动态切换或测试）
   */
  static setServiceContext(serviceContext: string): void {
    RedisKeyUtils._serviceContext = serviceContext;
  }

  /**
   * 重置缓存（主要用于测试）
   */
  static resetCache(): void {
    RedisKeyUtils._currentServerId = null;
    RedisKeyUtils._serviceContext = null;
  }

  /**
   * 从完整键名中提取业务键名
   */
  static extractBusinessKey(fullKey: string): string {
    // 移除前缀，只保留业务键名
    const parts = fullKey.split(':');

    if (parts.length >= 3) {
      const dataType = parts[2]; // 第三部分是数据类型

      if (dataType === 'global' || dataType === 'cross') {
        // 🔥 优化：global和cross类型去掉了服务前缀
        // 格式：{env}:{project}:{dataType}:{businessKey}
        return parts.slice(3).join(':');
      } else if (dataType.startsWith('server')) {
        // server类型保留服务前缀
        // 格式：{env}:{project}:server{id}:{service}:{businessKey}
        return parts.slice(4).join(':');
      }
    }

    return fullKey;
  }

  /**
   * 解析键名结构
   */
  static parseKeyStructure(fullKey: string): {
    environment?: string;
    project?: string;
    dataType?: DataType;
    serverId?: string;
    service?: string;
    businessKey?: string;
  } {
    const parts = fullKey.split(':');
    
    if (parts.length < 4) {
      return { businessKey: fullKey };
    }

    const [environment, project, dataType, ...restParts] = parts;

    if (dataType === 'global' || dataType === 'cross') {
      // 🔥 优化：global和cross类型去掉了服务前缀
      // 格式：{env}:{project}:{dataType}:{businessKey}
      return {
        environment,
        project,
        dataType,
        service: undefined, // global和cross类型没有服务前缀
        businessKey: restParts.join(':'),
      };
    } else if (dataType === 'server') {
      // 🔧 新格式：server类型使用冒号分隔
      // 格式：{env}:{project}:server:{serverId}:{service}:{businessKey}
      const [serverId, service, ...businessKeyParts] = restParts;
      return {
        environment,
        project,
        dataType,
        serverId,
        service,
        businessKey: businessKeyParts.join(':'),
      };
    }
  }

  /**
   * 构建搜索模式
   */
  static buildSearchPattern(
    basePrefix: string,
    pattern: string,
    options: RedisKeyOptions = {}
  ): string {
    const { dataType, serverId, serviceContext } = options;
    
    if (dataType) {
      // 构建特定数据类型的搜索模式
      return RedisKeyUtils.buildDataTypeKey(basePrefix, pattern, options);
    } else {
      // 搜索所有数据类型
      const cleanBasePrefix = basePrefix.replace(/:$/, '');
      const service = serviceContext || process.env.MICROSERVICE_NAME || 'default';
      return `${cleanBasePrefix}:*:${service}:${pattern}`;
    }
  }

  /**
   * 验证键名格式
   */
  static validateKeyFormat(key: string): boolean {
    if (!key || typeof key !== 'string') {
      return false;
    }

    // 检查键名长度
    if (key.length > 512) {
      return false;
    }

    // 检查是否包含非法字符
    const invalidChars = /[\s\n\r\t]/;
    return !invalidChars.test(key);
  }

  /**
   * 构建前缀配置
   */
  static buildPrefixConfig(environment: string, project: string): RedisPrefixConfig {
    const basePrefix = `${environment}:${project}:`;
    
    return {
      environment,
      project,
      basePrefix,
    };
  }
}

/**
 * 便捷函数：构建数据类型键名
 * 🎯 简化：默认为server类型（当前区服缓存）
 */
export function buildDataTypeKey(
  basePrefix: string,
  key: string,
  dataType?: DataType,
  serverId?: string,
  serviceContext?: string
): string {
  return RedisKeyUtils.buildDataTypeKey(basePrefix, key, {
    dataType, // 默认值在工具类中处理
    serverId,
    serviceContext,
  });
}

/**
 * 便捷函数：获取当前区服ID
 */
export function getCurrentServerId(): string {
  return RedisKeyUtils.getCurrentServerId();
}
