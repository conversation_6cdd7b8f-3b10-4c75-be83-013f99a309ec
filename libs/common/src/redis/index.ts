// Redis 模块统一导出

// 模块
export { RedisModule } from './redis.module';

// 类型定义
export { DataType, DATA_TYPES, RedisKeyOptions, RedisPrefixConfig } from './types/redis.types';

// 工具类
export { RedisKeyUtils, buildDataTypeKey, getCurrentServerId } from './utils/redis-key.utils';

// 服务
export { RedisService, CacheTTL } from './redis.service';
export { RedisHealthService, RedisHealthStatus, RedisPerformanceMetrics } from './redis-health.service';
export { RedisCacheService } from './redis-cache.service';
export { RedisQueueService, QueueJobData, QueueStats, JobResult } from './redis-queue.service';
export { RedisPubSubService, PubSubMessage, SubscriptionInfo, PubSubStats } from './redis-pubsub.service';
export { RedisLockService, LockOptions, LockInfo, LockStats } from './redis-lock.service';
export { RedisProtectionService, ProtectionOptions, CacheStats as ProtectionCacheStats } from './redis-protection.service';
export { RedisBloomFilterService, BloomFilterConfig, BloomFilterStats } from './redis-bloom-filter.service';
export { RedisMonitoringService, CacheMonitoringReport, CacheAlert } from './redis-monitoring.service';

// 缓存抽象层
export { CacheManagerService } from './cache/cache-manager.service';
export { BaseCacheRepository } from './cache/cache-repository';
export { CacheInterceptor } from './cache/cache.interceptor';

// Spring Boot风格缓存装饰器 - 推荐使用
export {
  Cacheable,
  CacheEvict,
  CachePut,
  CacheableOptions,
  CacheEvictOptions,
  CachePutOptions,
} from './cache/cache.decorators';

// 缓存常量
export {
  CACHEABLE_METADATA,
  CACHE_EVICT_METADATA,
  CACHE_PUT_METADATA,
} from './cache/cache.constants';

// 缓存工具函数
export {
  resolveCacheKey,
  resolveCacheKeys,
  evaluateCondition,
  CacheConditions,
} from './cache/cache.utils';

// 表达式解析器
export {
  ExpressionParser,
  ExpressionContext,
} from './cache/expression-parser';

// 数据源
export {
  BaseDataSource,
  HttpDataSource,
  DatabaseDataSource,
  MemoryDataSource,
  CompositeDataSource,
  LazyDataSource,
  RetryDataSource,
  DataSourceFactory,
} from './cache/data-sources';

// 缓存策略
export {
  CacheAsideStrategy,
  WriteThroughStrategy,
  WriteBehindStrategy,
  RefreshAheadStrategy,
  MultiLevelStrategy,
  MasterSlaveSync,
  EventualConsistencySync,
  CacheStrategyFactory,
} from './cache/cache-strategies';

// 缓存接口和类型
export {
  CacheOptions,
  CacheKeyOptions,
  CacheResult,
  DataSource,
  CacheRepository,
  CacheManager,
  CacheStats,
  CacheMode,
  CacheEvent,
  CacheMetadata,

  CacheStrategy,
  CacheSyncStrategy,
  CacheWarmer,
  CacheListener,
  CacheConfig,
} from './cache/cache.interfaces';

// 配置
export { 
  getCacheProtectionConfig, 
  getScenarioConfig, 
  validateConfig,
  CacheProtectionConfig,
  gameScenarioConfigs 
} from './config/cache-protection.config';

// 类型定义
export interface RedisModuleOptions {
  host?: string;
  port?: number;
  password?: string;
  db?: number;
  keyPrefix?: string;
  cluster?: boolean;
  sentinel?: boolean;
}

export interface RedisConnectionInfo {
  host: string;
  port: number;
  status: 'connected' | 'disconnected' | 'error';
  uptime: number;
  memoryUsage: number;
  connectedClients: number;
}

// 常量
export const REDIS_CONSTANTS = {
  DEFAULT_TTL: 3600,
  MAX_TTL: 86400 * 7, // 7天
  MIN_TTL: 60,        // 1分钟
  DEFAULT_LOCK_TIMEOUT: 30,
  MAX_LOCK_TIMEOUT: 300,
  DEFAULT_RETRY_DELAY: 100,
  MAX_RETRIES: 10,
} as const;

// 错误类型
export class RedisError extends Error {
  constructor(message: string, public readonly code?: string) {
    super(message);
    this.name = 'RedisError';
  }
}

export class CacheError extends Error {
  constructor(message: string, public readonly key?: string) {
    super(message);
    this.name = 'CacheError';
  }
}

export class BloomFilterError extends Error {
  constructor(message: string, public readonly filterName?: string) {
    super(message);
    this.name = 'BloomFilterError';
  }
}
