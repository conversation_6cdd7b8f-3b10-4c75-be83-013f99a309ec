import { Module, Global } from '@nestjs/common';
import { DiscoveryModule } from '@nestjs/core';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { CacheInterceptor } from './cache.interceptor';
import { CacheModuleConfigurator } from './cache-module.configurator';

/**
 * 缓存模块
 * 
 * 职责：
 * 1. 提供CacheInterceptor作为全局拦截器
 * 2. 提供CacheModuleConfigurator用于自动配置
 * 3. 管理缓存相关的依赖注入
 * 
 * 架构优势：
 * - 清晰的模块边界
 * - 正确的依赖注入
 * - 避免循环依赖
 * - 支持测试和扩展
 */
@Global()
@Module({
  imports: [
    DiscoveryModule, // 用于扫描装饰器
  ],
  providers: [
    // 🔧 全局拦截器（支持HTTP和RPC）
    {
      provide: APP_INTERCEPTOR,
      useClass: CacheInterceptor,
    },
    
    // 🔧 缓存拦截器实例（用于依赖注入）
    CacheInterceptor,
    
    // 🔧 缓存模块配置器（用于自动配置）
    CacheModuleConfigurator,
  ],
  exports: [
    CacheInterceptor,
    CacheModuleConfigurator,
  ],
})
export class CacheModule {
  constructor(
    private readonly configurator: CacheModuleConfigurator
  ) {
    // 构造函数中注入配置器，确保初始化顺序
    console.log('🔧 CacheModule initialized with configurator');
  }
}
