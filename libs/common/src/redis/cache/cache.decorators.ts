/**
 * Spring Boot风格的缓存装饰器
 * 支持 #{paramName} 表达式语法
 */

import { SetMetadata } from '@nestjs/common';
import { CacheOptions } from './cache.interfaces';
import { ExpressionParser, ExpressionContext } from './expression-parser';
import { DataType } from '../types/redis.types';

// 元数据键
export const CACHEABLE_METADATA = 'cache:cacheable';
export const CACHE_EVICT_METADATA = 'cache:evict';
export const CACHE_PUT_METADATA = 'cache:put';

/**
 * 缓存配置接口
 */
export interface CacheableOptions {
  /** 缓存键模板，支持 #{paramName} 语法 */
  key?: string;
  /** 缓存键数组，支持多个键 */
  keys?: string[];
  /** 缓存仓库名称 */
  repository?: string;
  /** 缓存选项 */
  ttl?: number;
  /** 缓存条件，支持 #{paramName != null} 语法 */
  condition?: string;
  /** 排除条件，基于返回值 */
  unless?: string;
  /** 参数名列表（可选，用于更好的类型安全） */
  paramNames?: string[];

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

export interface CacheEvictOptions {
  /** 缓存键模板或数组 */
  key?: string | string[];
  /** 缓存仓库名称 */
  repository?: string;
  /** 是否清除所有条目 */
  allEntries?: boolean;
  /** 是否在方法调用前清除 */
  beforeInvocation?: boolean;
  /** 清除条件 */
  condition?: string;
  /** 参数名列表 */
  paramNames?: string[];

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

export interface CachePutOptions {
  /** 缓存键模板 */
  key?: string;
  /** 缓存仓库名称 */
  repository?: string;
  /** 缓存TTL */
  ttl?: number;
  /** 缓存条件 */
  condition?: string;
  /** 排除条件 */
  unless?: string;
  /** 参数名列表 */
  paramNames?: string[];

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

/**
 * @Cacheable 装饰器
 *
 * @example
 * ```typescript
 * @Cacheable({
 *   key: 'user:id:#{id}',
 *   ttl: 300,
 *   condition: '#{id != null}'
 * })
 * async findById(id: string): Promise<User> {
 *   // ...
 * }
 * ```
 */
export function Cacheable(options: CacheableOptions = {}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    // 尝试自动提取参数名（仅在开发环境）
    const autoParamNames = ExpressionParser.extractParameterNames(originalMethod);
    const paramNames = options.paramNames || autoParamNames;

    const metadata = {
      key: options.key,
      keys: options.keys,
      repository: options.repository || target.constructor.name.toLowerCase(),
      ttl: options.ttl,
      condition: options.condition,
      unless: options.unless,
      paramNames,
      originalMethod,
      methodName: propertyKey,

      // 新增字段
      dataType: options.dataType,
      serverId: options.serverId,
    };

    // 🔧 设置缓存元数据
    SetMetadata(CACHEABLE_METADATA, metadata)(target, propertyKey, descriptor);

    // 🔧 自动应用CacheInterceptor（延迟导入避免循环依赖）
    try {
      const { CacheInterceptor } = require('./cache.interceptor');
      UseInterceptors(CacheInterceptor)(target, propertyKey, descriptor);
    } catch (error) {
      console.warn('Failed to apply CacheInterceptor to @Cacheable:', error.message);
    }

    return descriptor;
  };
}

/**
 * @CacheEvict 装饰器
 * 
 * @example
 * ```typescript
 * @CacheEvict({
 *   key: ['user:id:#{id}', 'user:username:#{result.username}'],
 *   condition: '#{id != null}'
 * })
 * async updateUser(id: string, data: UpdateUserDto): Promise<User> {
 *   // ...
 * }
 * ```
 */
export function CacheEvict(options: CacheEvictOptions = {}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    const autoParamNames = ExpressionParser.extractParameterNames(originalMethod);
    const paramNames = options.paramNames || autoParamNames;
    
    // 标准化key为数组格式
    let keys: string[] = [];
    if (typeof options.key === 'string') {
      keys = [options.key];
    } else if (Array.isArray(options.key)) {
      keys = options.key;
    }
    
    const metadata = {
      keys,
      repository: options.repository || target.constructor.name.toLowerCase(),
      allEntries: options.allEntries || false,
      beforeInvocation: options.beforeInvocation || false,
      condition: options.condition,
      paramNames,
      originalMethod,
      methodName: propertyKey,

      // 新增字段
      dataType: options.dataType,
      serverId: options.serverId,
    };

    SetMetadata(CACHE_EVICT_METADATA, metadata)(target, propertyKey, descriptor);
    return descriptor;
  };
}

/**
 * @CachePut 装饰器
 * 
 * @example
 * ```typescript
 * @CachePut({
 *   key: 'user:id:#{id}',
 *   ttl: 300,
 *   condition: '#{id != null}'
 * })
 * async updateUser(id: string, data: UpdateUserDto): Promise<User> {
 *   // ...
 * }
 * ```
 */
export function CachePut(options: CachePutOptions = {}) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    const autoParamNames = ExpressionParser.extractParameterNames(originalMethod);
    const paramNames = options.paramNames || autoParamNames;
    
    const metadata = {
      key: options.key,
      repository: options.repository || target.constructor.name.toLowerCase(),
      ttl: options.ttl,
      condition: options.condition,
      unless: options.unless,
      paramNames,
      originalMethod,
      methodName: propertyKey,

      // 新增字段
      dataType: options.dataType,
      serverId: options.serverId,
    };

    SetMetadata(CACHE_PUT_METADATA, metadata)(target, propertyKey, descriptor);
    return descriptor;
  };
}

/**
 * 工具函数：解析缓存键
 */
export function resolveCacheKey(
  keyTemplate: string,
  args: any[],
  paramNames: string[],
  result?: any,
  target?: any,
  methodName?: string
): string {
  const context: ExpressionContext = {
    args,
    paramNames,
    result,
    target,
    methodName,
  };
  
  return ExpressionParser.parse(keyTemplate, context);
}

/**
 * 工具函数：解析缓存键数组
 */
export function resolveCacheKeys(
  keyTemplates: string[],
  args: any[],
  paramNames: string[],
  result?: any,
  target?: any,
  methodName?: string
): string[] {
  const context: ExpressionContext = {
    args,
    paramNames,
    result,
    target,
    methodName,
  };
  
  return ExpressionParser.parseArray(keyTemplates, context);
}

/**
 * 工具函数：评估条件
 */
export function evaluateCondition(
  condition: string | undefined,
  args: any[],
  paramNames: string[],
  result?: any,
  target?: any,
  methodName?: string
): boolean {
  if (!condition) {
    return true;
  }
  
  const context: ExpressionContext = {
    args,
    paramNames,
    result,
    target,
    methodName,
  };
  
  return ExpressionParser.evaluateCondition(condition, context);
}

/**
 * 缓存条件工具类
 * 提供常用的缓存条件判断方法
 */
export class CacheConditions {
  /**
   * 验证参数是否有效
   */
  static validArgs(args: any[]): boolean {
    return args.length > 0 && args.every(arg => arg != null && arg !== '');
  }

  /**
   * 验证结果是否有效
   */
  static validResult(result: any): boolean {
    if (result == null) return false;
    if (Array.isArray(result)) return result.length > 0;
    if (typeof result === 'object') return Object.keys(result).length > 0;
    return true;
  }

  /**
   * 验证用户ID是否有效
   */
  static validUserId(userId: string): boolean {
    return typeof userId === 'string' && userId.length > 0 && userId !== 'undefined';
  }

  /**
   * 验证分页参数是否有效
   */
  static validPagination(page: number, size: number): boolean {
    return Number.isInteger(page) && Number.isInteger(size) && page > 0 && size > 0 && size <= 100;
  }
}
