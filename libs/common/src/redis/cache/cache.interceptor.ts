import {
  Injectable,
  NestInterceptor,
  Exec<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
  Inject,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { CacheManagerService } from './cache-manager.service';
import { DataType } from '../types/redis.types';

import {
  CACHEABLE_METADATA,
  CACHE_EVICT_METADATA,
  CACHE_PUT_METADATA,
  resolveCacheKey,
  resolveCacheKeys,
  evaluateCondition,
} from './cache.decorators';

/**
 * Spring Boot风格缓存装饰器的元数据接口
 */
interface CacheableMetadata {
  key?: string;
  keys?: string[];
  repository?: string;
  ttl?: number;
  condition?: string;
  unless?: string;
  paramNames?: string[];
  originalMethod?: Function;
  name?: string;

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

interface CacheEvictMetadata {
  keys?: string[];
  repository?: string;
  allEntries?: boolean;
  beforeInvocation?: boolean;
  condition?: string;
  paramNames?: string[];
  originalMethod?: Function;
  name?: string;

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

interface CachePutMetadata {
  key?: string;
  repository?: string;
  ttl?: number;
  condition?: string;
  unless?: string;
  paramNames?: string[];
  originalMethod?: Function;
  name?: string;

  // 新增：数据类型支持
  dataType?: DataType;
  serverId?: string;
}

/**
 * 缓存拦截器
 * 支持Spring Boot风格的缓存装饰器语法
 */
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(
    private readonly cacheManager: CacheManagerService,
    private readonly reflector: Reflector,
    @Inject('REDIS_SERVICE_CONTEXT') private readonly serviceContext: string = 'unknown',
  ) {}

  /**
   * 从请求中提取serverId
   */
  private extractServerIdFromRequest(context: ExecutionContext): string | undefined {
    try {
      const request = context.switchToHttp().getRequest();
      return request?.headers?.['x-server-id'] ||
             request?.query?.serverId ||
             request?.params?.serverId ||
             process.env.SERVER_ID;
    } catch (error) {
      // 非HTTP上下文，返回环境变量中的SERVER_ID
      return process.env.SERVER_ID;
    }
  }

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    const handler = context.getHandler();
    const target = context.getClass();
    const name = handler.name;

    // 🔧 调试：记录所有拦截器调用
    this.logger.debug(`🔍 CacheInterceptor called for ${target.name}.${name}`);

    // 获取Spring Boot风格装饰器元数据
    const cacheableMetadata = this.reflector.get<CacheableMetadata>(CACHEABLE_METADATA, handler);
    const cacheEvictMetadata = this.reflector.get<CacheEvictMetadata>(CACHE_EVICT_METADATA, handler);
    const cachePutMetadata = this.reflector.get<CachePutMetadata>(CACHE_PUT_METADATA, handler);

    // 调试日志：检查是否有缓存装饰器
    if (cacheableMetadata || cacheEvictMetadata || cachePutMetadata) {
      this.logger.log(`🔍 Cache interceptor triggered for ${target.name}.${name}`);
      if (cacheableMetadata) this.logger.log(`  - @Cacheable: ${JSON.stringify(cacheableMetadata)}`);
      if (cacheEvictMetadata) this.logger.log(`  - @CacheEvict: ${JSON.stringify(cacheEvictMetadata)}`);
      if (cachePutMetadata) this.logger.log(`  - @CachePut: ${JSON.stringify(cachePutMetadata)}`);
    }

    // 如果没有任何缓存装饰器，直接执行方法
    if (!cacheableMetadata && !cacheEvictMetadata && !cachePutMetadata) {
      return next.handle();
    }

    // 获取方法参数和上下文信息
    const args = context.getArgs();
    const targetInstance = target;

    try {
      // 1. 处理 @CacheEvict (beforeInvocation = true)
      if (cacheEvictMetadata?.beforeInvocation) {
        await this.handleCacheEvict(cacheEvictMetadata, args, null, name, targetInstance);
      }

      // 2. 处理 @Cacheable - 尝试从缓存获取
      if (cacheableMetadata) {
        const cachedResult = await this.handleCacheable(cacheableMetadata, args, name, targetInstance);
        if (cachedResult !== undefined) {
          this.logger.log(`✅ Cache hit for ${name}`);
          return of(cachedResult);
        }
        this.logger.log(`❌ Cache miss for ${name}`);
      }

      // 3. 执行原方法
      return next.handle().pipe(
        tap(async (result) => {
          try {
            // 4. 处理 @Cacheable 结果缓存
            if (cacheableMetadata) {
              await this.handleCacheableResult(cacheableMetadata, args, result, name, targetInstance);
            }

            // 5. 处理 @CachePut
            if (cachePutMetadata) {
              await this.handleCachePut(cachePutMetadata, args, result, name, targetInstance);
            }

            // 6. 处理 @CacheEvict (beforeInvocation = false，默认)
            if (cacheEvictMetadata && !cacheEvictMetadata.beforeInvocation) {
              await this.handleCacheEvict(cacheEvictMetadata, args, result, name, targetInstance);
            }
          } catch (error) {
            this.logger.error(`❌ Cache operation failed for ${name}: ${error.message}`);
          }
        }),
        catchError((error) => {
          this.logger.error(`❌ Method execution failed for ${name}: ${error.message}`);
          throw error;
        }),
      );
    } catch (error) {
      this.logger.error(`❌ Cache interceptor error for ${name}: ${error.message}`);
      return next.handle();
    }
  }

  // ==================== @Cacheable 处理 ====================

  private async handleCacheable(
    metadata: CacheableMetadata,
    args: any[],
    name: string,
    target: any,
  ): Promise<any> {
    // 检查条件
    if (metadata.condition && !evaluateCondition(metadata.condition, args, metadata.paramNames)) {
      this.logger.debug(`Cache condition not met for ${name}`);
      return undefined;
    }

    if (!metadata.key) {
      this.logger.warn(`No cache key specified for ${name}`);
      return undefined;
    }

    const key = resolveCacheKey(metadata.key, args, metadata.paramNames, undefined, target, name);
    const repository = this.cacheManager.getRepository(metadata.repository || 'default');

    // 构建缓存选项，包含dataType
    const cacheOptions = {
      dataType: metadata.dataType,
      serverId: metadata.serverId
    };

    try {
      const result = await repository.get(key, cacheOptions);
      if (result.hit && result.data !== null) {
        // 检查 unless 条件
        if (metadata.unless && !evaluateCondition(metadata.unless, args, metadata.paramNames, result.data)) {
          this.logger.debug(`Cache unless condition met for ${name}, ignoring cached result`);
          return undefined;
        }

        this.logger.debug(`Cache hit for ${name} with key: ${key} (dataType: ${metadata.dataType || 'server'})`);
        return result.data;
      }
    } catch (error) {
      this.logger.error(`Cache get failed for ${name}: ${error.message}`);
    }

    return undefined;
  }

  private async handleCacheableResult(
    metadata: CacheableMetadata,
    args: any[],
    result: any,
    name: string,
    target: any,
  ): Promise<void> {
    // 检查 unless 条件
    if (metadata.unless && !evaluateCondition(metadata.unless, args, metadata.paramNames, result)) {
      this.logger.debug(`Cache unless condition met for ${name}, not caching result`);
      return;
    }

    if (!metadata.key) {
      return;
    }

    const key = resolveCacheKey(metadata.key, args, metadata.paramNames, result, target, name);
    const repository = this.cacheManager.getRepository(metadata.repository || 'default');

    try {
      // 构建缓存选项，包含dataType
      const options = {
        ttl: metadata.ttl,
        dataType: metadata.dataType,
        serverId: metadata.serverId
      };
      await repository.set(key, result, options);
      this.logger.log(`✅ Cached result for ${name} with key: ${key} (dataType: ${metadata.dataType || 'server'})`);
    } catch (error) {
      this.logger.error(`❌ Cache set failed for ${name}: ${error.message}`);
    }
  }

  // ==================== @CachePut 处理 ====================

  private async handleCachePut(
    metadata: CachePutMetadata,
    args: any[],
    result: any,
    name: string,
    target: any,
  ): Promise<void> {
    // 检查条件
    if (metadata.condition && !evaluateCondition(metadata.condition, args, metadata.paramNames)) {
      this.logger.debug(`CachePut condition not met for ${name}`);
      return;
    }

    // 检查 unless 条件
    if (metadata.unless && !evaluateCondition(metadata.unless, args, metadata.paramNames, result)) {
      this.logger.debug(`CachePut unless condition met for ${name}`);
      return;
    }

    if (!metadata.key) {
      this.logger.warn(`No cache key specified for CachePut in ${name}`);
      return;
    }

    const key = resolveCacheKey(metadata.key, args, metadata.paramNames, result, target, name);
    const repository = this.cacheManager.getRepository(metadata.repository || 'default');

    try {
      // 构建缓存选项，包含dataType
      const options = {
        ttl: metadata.ttl,
        dataType: metadata.dataType,
        serverId: metadata.serverId
      };
      await repository.set(key, result, options);
      this.logger.debug(`✅ CachePut completed for ${name} with key: ${key} (dataType: ${metadata.dataType || 'server'})`);
    } catch (error) {
      this.logger.error(`❌ CachePut failed for ${name}: ${error.message}`);
    }
  }

  // ==================== @CacheEvict 处理 ====================

  private async handleCacheEvict(
    metadata: CacheEvictMetadata,
    args: any[],
    result: any,
    name: string,
    target: any,
  ): Promise<void> {
    // 检查条件
    if (metadata.condition && !evaluateCondition(metadata.condition, args, metadata.paramNames, result)) {
      this.logger.debug(`CacheEvict condition not met for ${name}`);
      return;
    }

    const repository = this.cacheManager.getRepository(metadata.repository || 'default');

    // 构建缓存选项，包含dataType
    const cacheOptions = {
      dataType: metadata.dataType,
      serverId: metadata.serverId
    };

    try {
      if (metadata.allEntries) {
        // 清除所有条目
        const cleared = await repository.clear(undefined, cacheOptions);
        this.logger.debug(`✅ CacheEvict cleared ${cleared} entries for ${name} (dataType: ${metadata.dataType || 'server'})`);
      } else if (metadata.keys && metadata.keys.length > 0) {
        // 清除多个键
        const keys = resolveCacheKeys(metadata.keys, args, metadata.paramNames, result, target, name);
        let deletedCount = 0;
        for (const key of keys) {
          const deleted = await repository.delete(key, cacheOptions);
          if (deleted) deletedCount++;
          this.logger.debug(`CacheEvict ${deleted ? 'deleted' : 'attempted to delete'} key: ${key} (dataType: ${metadata.dataType || 'server'})`);
        }
        this.logger.debug(`✅ CacheEvict deleted ${deletedCount}/${keys.length} keys for ${name}`);
      }
    } catch (error) {
      this.logger.error(`❌ CacheEvict failed for ${name}: ${error.message}`);
    }
  }
}
