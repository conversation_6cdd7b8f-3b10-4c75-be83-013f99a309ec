/**
 * 缓存工具函数
 * 
 * 职责：
 * - 提供缓存键解析功能
 * - 提供条件评估功能
 * - 提供常用的缓存条件判断方法
 * 
 * 架构优势：
 * - 纯函数，无副作用
 * - 可独立测试
 * - 避免循环依赖
 * - 可被拦截器和装饰器安全导入
 */

import { ExpressionParser, ExpressionContext } from './expression-parser';

/**
 * 工具函数：解析缓存键
 * 
 * @param keyTemplate 缓存键模板，支持表达式
 * @param args 方法参数数组
 * @param paramNames 参数名称数组
 * @param result 方法执行结果（可选）
 * @param target 目标对象（可选）
 * @param methodName 方法名称（可选）
 * @returns 解析后的缓存键
 */
export function resolveCacheKey(
  keyTemplate: string,
  args: any[],
  paramNames: string[],
  result?: any,
  target?: any,
  methodName?: string
): string {
  const context: ExpressionContext = {
    args,
    paramNames,
    result,
    target,
    methodName,
  };
  
  return ExpressionParser.parse(keyTemplate, context);
}

/**
 * 工具函数：解析缓存键数组
 * 
 * @param keyTemplates 缓存键模板数组
 * @param args 方法参数数组
 * @param paramNames 参数名称数组
 * @param result 方法执行结果（可选）
 * @param target 目标对象（可选）
 * @param methodName 方法名称（可选）
 * @returns 解析后的缓存键数组
 */
export function resolveCacheKeys(
  keyTemplates: string[],
  args: any[],
  paramNames: string[],
  result?: any,
  target?: any,
  methodName?: string
): string[] {
  const context: ExpressionContext = {
    args,
    paramNames,
    result,
    target,
    methodName,
  };
  
  return ExpressionParser.parseArray(keyTemplates, context);
}

/**
 * 工具函数：评估条件
 * 
 * @param condition 条件表达式
 * @param args 方法参数数组
 * @param paramNames 参数名称数组
 * @param result 方法执行结果（可选）
 * @param target 目标对象（可选）
 * @param methodName 方法名称（可选）
 * @returns 条件评估结果
 */
export function evaluateCondition(
  condition: string | undefined,
  args: any[],
  paramNames: string[],
  result?: any,
  target?: any,
  methodName?: string
): boolean {
  if (!condition) {
    return true;
  }
  
  const context: ExpressionContext = {
    args,
    paramNames,
    result,
    target,
    methodName,
  };
  
  return ExpressionParser.evaluateCondition(condition, context);
}

/**
 * 缓存条件工具类
 * 提供常用的缓存条件判断方法
 */
export class CacheConditions {
  /**
   * 验证参数是否有效
   */
  static validArgs(args: any[]): boolean {
    return args.length > 0 && args.every(arg => arg != null && arg !== '');
  }

  /**
   * 验证结果是否有效
   */
  static validResult(result: any): boolean {
    if (result == null) return false;
    if (Array.isArray(result)) return result.length > 0;
    if (typeof result === 'object') return Object.keys(result).length > 0;
    return true;
  }

  /**
   * 验证用户ID是否有效
   */
  static validUserId(userId: string): boolean {
    return typeof userId === 'string' && userId.length > 0 && userId !== 'undefined';
  }

  /**
   * 验证分页参数是否有效
   */
  static validPagination(page: number, size: number): boolean {
    return Number.isInteger(page) && Number.isInteger(size) && page > 0 && size > 0 && size <= 100;
  }
}
