import { Injectable, OnModuleInit } from '@nestjs/common';
import { DiscoveryService, MetadataScanner, Reflector } from '@nestjs/core';
import { InstanceWrapper } from '@nestjs/core/injector/instance-wrapper';
import { CacheInterceptor } from './cache.interceptor';

/**
 * 缓存模块配置器
 * 
 * 职责：
 * 1. 在模块初始化时扫描所有带有@Cacheable装饰器的方法
 * 2. 自动为这些方法应用CacheInterceptor
 * 3. 提供清晰的依赖关系和错误处理
 * 
 * 优势：
 * - 避免循环依赖
 * - 集中管理拦截器应用逻辑
 * - 提供完整的错误处理和日志
 * - 支持单元测试
 */
@Injectable()
export class CacheModuleConfigurator implements OnModuleInit {
  constructor(
    private readonly discoveryService: DiscoveryService,
    private readonly metadataScanner: MetadataScanner,
    private readonly reflector: Reflector,
    private readonly cacheInterceptor: CacheInterceptor,
  ) {}

  async onModuleInit() {
    await this.configureCacheInterceptors();
  }

  /**
   * 配置缓存拦截器
   * 扫描所有控制器和服务，为带有@Cacheable装饰器的方法应用拦截器
   */
  private async configureCacheInterceptors() {
    const providers = this.discoveryService.getProviders();
    const controllers = this.discoveryService.getControllers();
    
    // 处理所有提供者（服务）
    for (const wrapper of providers) {
      await this.processWrapper(wrapper, 'Provider');
    }
    
    // 处理所有控制器
    for (const wrapper of controllers) {
      await this.processWrapper(wrapper, 'Controller');
    }
  }

  /**
   * 处理单个包装器（控制器或服务）
   */
  private async processWrapper(wrapper: InstanceWrapper, type: string) {
    const { instance, metatype } = wrapper;
    
    if (!instance || !metatype) {
      return;
    }

    // 获取所有方法名
    const methodNames = this.metadataScanner.getAllMethodNames(
      Object.getPrototypeOf(instance)
    );

    for (const methodName of methodNames) {
      await this.processMethod(instance, metatype, methodName, type);
    }
  }

  /**
   * 处理单个方法
   */
  private async processMethod(
    instance: any,
    metatype: any,
    methodName: string,
    type: string
  ) {
    try {
      const methodRef = instance[methodName];
      
      if (typeof methodRef !== 'function') {
        return;
      }

      // 检查是否需要缓存拦截器
      const requiresCacheInterceptor = this.reflector.get<boolean>(
        'REQUIRES_CACHE_INTERCEPTOR',
        methodRef
      );

      if (!requiresCacheInterceptor) {
        return;
      }

      // 检查是否有@Cacheable元数据
      const cacheableMetadata = this.reflector.get(
        'cacheable_metadata',
        methodRef
      );

      if (!cacheableMetadata) {
        console.warn(
          `⚠️ Method ${metatype.name}.${methodName} marked for cache interceptor but missing @Cacheable metadata`
        );
        return;
      }

      // 应用拦截器逻辑
      await this.applyCacheInterceptor(instance, metatype, methodName, type);
      
      console.log(
        `✅ Applied CacheInterceptor to ${type} ${metatype.name}.${methodName}`
      );

    } catch (error) {
      console.error(
        `❌ Failed to process method ${metatype.name}.${methodName}:`,
        error.message
      );
    }
  }

  /**
   * 应用缓存拦截器
   * 这里可以实现具体的拦截器应用逻辑
   */
  private async applyCacheInterceptor(
    instance: any,
    metatype: any,
    methodName: string,
    type: string
  ) {
    // 🔧 这里可以实现具体的拦截器应用逻辑
    // 例如：动态包装方法、注册拦截器等
    
    // 目前我们主要依赖于CacheInterceptor在运行时的元数据检查
    // 这个方法为将来的扩展预留了空间
    
    console.debug(
      `🔧 Cache interceptor configuration applied for ${type} ${metatype.name}.${methodName}`
    );
  }
}
