import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { RedisService } from '../redis.service';
import { RedisProtectionService } from '../redis-protection.service';
import { RedisPubSubService } from '../redis-pubsub.service';
import { BaseCacheRepository } from './cache-repository';
import {
  CacheManager,
  CacheRepository,
  DataSource,
  CacheStats,
  CacheConfig,
  CacheListener,
  CacheEvent,
  CacheResult,
} from './cache.interfaces';

class DefaultCacheRepository<T> extends BaseCacheRepository<T> {
  constructor(
    name: string,
    redisService: RedisService,
    protectionService: RedisProtectionService,
    pubSubService: RedisPubSubService,
    dataSource?: DataSource<T>,
  ) {
    super(name, redisService, protectionService, pubSubService, dataSource);
  }
}

@Injectable()
export class CacheManagerService implements CacheManager, OnModuleInit {
  private readonly logger = new Logger(CacheManagerService.name);
  private readonly repositories = new Map<string, CacheRepository<any>>();
  private readonly dataSources = new Map<string, DataSource<any>>();
  private readonly listeners: CacheListener[] = [];
  private readonly config: CacheConfig;

  constructor(
    private readonly redisService: RedisService,
    private readonly protectionService: RedisProtectionService,
    private readonly pubSubService: RedisPubSubService,
  ) {
    this.config = this.getDefaultConfig();
  }

  async onModuleInit() {
    // 🔧 等待Redis完全初始化完成
    await this.redisService.waitForInitialization();

    // 订阅缓存事件
    if (this.config.enableEvents) {
      await this.subscribeToEvents();
    }

    // 初始化预配置的存储库
    await this.initializeRepositories();

    this.logger.log('Cache manager initialized');
  }

  // ==================== 存储库管理 ====================

  getRepository<T>(name: string): CacheRepository<T> {
    let repository = this.repositories.get(name);
    
    if (!repository) {
      repository = this.createRepository<T>(name);
    }

    return repository as CacheRepository<T>;
  }

  createRepository<T>(name: string, dataSource?: DataSource<T>): CacheRepository<T> {
    if (this.repositories.has(name)) {
      this.logger.warn(`Repository ${name} already exists, returning existing instance`);
      return this.repositories.get(name) as CacheRepository<T>;
    }

    // 获取或使用提供的数据源
    const finalDataSource = dataSource || this.dataSources.get(name);

    const repository = new DefaultCacheRepository<T>(
      name,
      this.redisService,
      this.protectionService,
      this.pubSubService,
      finalDataSource,
    );

    this.repositories.set(name, repository);
    this.logger.log(`Created cache repository: ${name}`);

    return repository;
  }

  registerDataSource<T>(name: string, dataSource: DataSource<T>): void {
    this.dataSources.set(name, dataSource);
    this.logger.log(`Registered data source: ${name}`);
  }

  removeRepository(name: string): boolean {
    const removed = this.repositories.delete(name);
    if (removed) {
      this.logger.log(`Removed cache repository: ${name}`);
    }
    return removed;
  }

  // ==================== 全局操作 ====================

  async clearAll(): Promise<void> {
    const startTime = Date.now();
    let totalCleared = 0;

    for (const [name, repository] of this.repositories) {
      try {
        const cleared = await repository.clear();
        totalCleared += cleared;
        this.logger.log(`Cleared ${cleared} entries from repository: ${name}`);
      } catch (error) {
        this.logger.error(`Failed to clear repository ${name}: ${error.message}`);
      }
    }

    const duration = Date.now() - startTime;
    this.logger.log(`Cleared ${totalCleared} total cache entries in ${duration}ms`);
  }

  async warmupAll(): Promise<void> {
    const startTime = Date.now();

    for (const [name, repository] of this.repositories) {
      try {
        await this.warmupRepository(name);
      } catch (error) {
        this.logger.error(`Failed to warmup repository ${name}: ${error.message}`);
      }
    }

    const duration = Date.now() - startTime;
    this.logger.log(`Cache warmup completed in ${duration}ms`);
  }

  // ==================== 统计和监控 ====================

  getStats(): CacheStats {
    const repositoryStats: Record<string, any> = {};
    let totalRequests = 0;
    let totalHits = 0;
    let totalMisses = 0;
    let totalLoadTime = 0;
    let loadCount = 0;
    let minLoadTime = Infinity;
    let maxLoadTime = 0;

    for (const [name, repository] of this.repositories) {
      if ('getStats' in repository && typeof repository.getStats === 'function') {
        const stats = repository.getStats();
        repositoryStats[name] = {
          requests: stats.requests,
          hits: stats.hits,
          misses: stats.misses,
          hitRate: stats.hitRate,
        };

        totalRequests += stats.requests;
        totalHits += stats.hits;
        totalMisses += stats.misses;

        if (stats.avgLoadTime) {
          totalLoadTime += stats.avgLoadTime;
          loadCount++;
          minLoadTime = Math.min(minLoadTime, stats.avgLoadTime);
          maxLoadTime = Math.max(maxLoadTime, stats.avgLoadTime);
        }
      }
    }

    const hitRate = totalRequests > 0 ? totalHits / totalRequests : 0;
    const avgLoadTime = loadCount > 0 ? totalLoadTime / loadCount : 0;

    return {
      totalRequests,
      hits: totalHits,
      misses: totalMisses,
      hitRate,
      loadTime: {
        average: avgLoadTime,
        min: minLoadTime === Infinity ? 0 : minLoadTime,
        max: maxLoadTime,
      },
      repositories: repositoryStats,
    };
  }

  resetAllStats(): void {
    for (const [name, repository] of this.repositories) {
      if ('resetStats' in repository && typeof repository.resetStats === 'function') {
        repository.resetStats();
      }
    }
    this.logger.log('All cache statistics reset');
  }

  // ==================== 事件监听 ====================

  addListener(listener: CacheListener): void {
    this.listeners.push(listener);
    this.logger.log('Cache listener added');
  }

  removeListener(listener: CacheListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
      this.logger.log('Cache listener removed');
    }
  }

  // ==================== 缓存预热 ====================

  async warmupRepository(name: string, keys?: string[]): Promise<void> {
    const repository = this.repositories.get(name);
    if (!repository) {
      throw new Error(`Repository ${name} not found`);
    }

    const dataSource = this.dataSources.get(name);
    if (!dataSource?.load) {
      this.logger.warn(`No data source available for repository ${name}, skipping warmup`);
      return;
    }

    const startTime = Date.now();
    let warmedCount = 0;

    if (keys && keys.length > 0) {
      // 预热指定的键
      for (const key of keys) {
        try {
          await repository.getOrLoad(key, () => dataSource.load(key));
          warmedCount++;
        } catch (error) {
          this.logger.warn(`Failed to warmup key ${key}: ${error.message}`);
        }
      }
    } else {
      // 这里可以实现自动发现需要预热的键的逻辑
      this.logger.warn(`No keys specified for warmup of repository ${name}`);
    }

    const duration = Date.now() - startTime;
    this.logger.log(`Warmed up ${warmedCount} entries for repository ${name} in ${duration}ms`);
  }

  // ==================== 私有方法 ====================

  private async subscribeToEvents(): Promise<void> {
    await this.pubSubService.subscribeToEvent('cache', '*', (event: CacheEvent) => {
      this.handleCacheEvent(event);
    });
  }

  private handleCacheEvent(event: CacheEvent): void {
    // 分发事件给监听器
    for (const listener of this.listeners) {
      try {
        switch (event.type) {
          case 'hit':
            listener.onCacheHit?.(event);
            break;
          case 'miss':
            listener.onCacheMiss?.(event);
            break;
          case 'set':
            listener.onCacheSet?.(event);
            break;
          case 'delete':
            listener.onCacheDelete?.(event);
            break;
          case 'error':
            listener.onCacheError?.(event);
            break;
        }
      } catch (error) {
        this.logger.error(`Cache listener error: ${error.message}`);
      }
    }
  }

  private async initializeRepositories(): Promise<void> {
    for (const [name, config] of Object.entries(this.config.repositories)) {
      try {
        let dataSource: DataSource<any> | undefined;
        
        if (config.dataSource) {
          dataSource = this.dataSources.get(config.dataSource);
        }

        this.createRepository(name, dataSource);
      } catch (error) {
        this.logger.error(`Failed to initialize repository ${name}: ${error.message}`);
      }
    }
  }

  private getDefaultConfig(): CacheConfig {
    return {
      defaultTTL: 3600,
      defaultTTLVariance: 0.2,
      enableProtection: true,
      enableStats: true,
      enableEvents: true,
      maxKeyLength: 250,
      keyPrefix: 'cache',
      repositories: {},
      strategies: {},
      listeners: [],
    };
  }

  // ==================== 高级功能 ====================

  /**
   * 批量操作
   */
  async batchGet<T>(repository: string, keys: string[]): Promise<Map<string, CacheResult<T>>> {
    const repo = this.getRepository<T>(repository);
    const results = new Map<string, CacheResult<T>>();

    await Promise.all(
      keys.map(async (key) => {
        try {
          const result = await repo.get(key);
          results.set(key, result);
        } catch (error) {
          results.set(key, {
            hit: false,
            data: null,
            source: 'cache',
            loadTime: 0,
          });
        }
      })
    );

    return results;
  }

  /**
   * 批量设置
   */
  async batchSet<T>(repository: string, entries: Map<string, T>, options?: any): Promise<void> {
    const repo = this.getRepository<T>(repository);

    await Promise.all(
      Array.from(entries.entries()).map(([key, value]) =>
        repo.set(key, value, options)
      )
    );
  }

  /**
   * 获取存储库列表
   */
  getRepositoryNames(): string[] {
    return Array.from(this.repositories.keys());
  }

  /**
   * 检查存储库是否存在
   */
  hasRepository(name: string): boolean {
    return this.repositories.has(name);
  }
}
