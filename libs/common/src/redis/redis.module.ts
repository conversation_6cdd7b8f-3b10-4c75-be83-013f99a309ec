import { Module, Global, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MICROSERVICE_NAMES } from '@shared/constants';

import { RedisService } from './redis.service';
import { RedisHealthService } from './redis-health.service';
import { RedisCacheService } from './redis-cache.service';
import { RedisQueueService } from './redis-queue.service';
import { RedisPubSubService } from './redis-pubsub.service';
import { RedisLockService } from './redis-lock.service';
import { RedisProtectionService } from './redis-protection.service';
import { RedisBloomFilterService } from './redis-bloom-filter.service';
import { RedisMonitoringService } from './redis-monitoring.service';
import { CacheManagerService } from './cache/cache-manager.service';
import { DataSourceFactory } from './cache/data-sources';
import { CacheStrategyFactory } from './cache/cache-strategies';
import { CacheModule } from './cache/cache.module';

/**
 * Redis模块选项接口
 */
export interface RedisModuleOptions {
  service?: string;           // 服务名称（必须显式指定）
  database?: number;          // Redis数据库（可选，自动分配）
  serverId?: string;          // 区服ID（可选，用于分区分服）
  useFactory?: (...args: any[]) => any;
  inject?: any[];
}

/**
 * Redis模块 - 支持服务上下文的v2.0架构
 */
@Global()
@Module({})
export class RedisModule {
  /**
   * 异步模块工厂 - 支持服务上下文
   */
  static forRootAsync(options?: RedisModuleOptions): DynamicModule {
    return {
      module: RedisModule,
      imports: [ConfigModule],
      providers: [
        // 服务上下文提供者
        {
          provide: 'REDIS_SERVICE_CONTEXT',
          useFactory: (configService: ConfigService) => {
            return options?.service ||
                   configService.get('MICROSERVICE_NAME') ||
                   'unknown';
          },
          inject: [ConfigService],
        },

        // 区服ID提供者
        {
          provide: 'REDIS_SERVER_ID',
          useFactory: (configService: ConfigService) => {
            // 优先级：模块配置 > 系统环境变量 > ConfigService > 默认值
            return options?.serverId ||
                   process.env.SERVER_ID ||
                   configService.get('SERVER_ID') ||
                   '1';
          },
          inject: [ConfigService],
        },

        // Redis连接提供者
        {
          provide: 'REDIS_CONNECTION',
          useFactory: async (configService: ConfigService, serviceContext: string, serverId: string) => {
            // 构建配置
            const baseConfig = options?.useFactory
              ? await options.useFactory(configService)
              : this.getDefaultConfig(configService);

            // 增强配置
            const enhancedConfig = {
              ...baseConfig,
              db: options?.database || this.getServiceDatabase(serviceContext),
              keyPrefix: this.buildServicePrefix(serviceContext, configService), // 连接级前缀
            };

            const redisService = new RedisService(enhancedConfig, serviceContext, serverId);
            await redisService.onModuleInit();
            return redisService;
          },
          inject: [ConfigService, 'REDIS_SERVICE_CONTEXT', 'REDIS_SERVER_ID'],
        },

        // Redis服务别名
        {
          provide: RedisService,
          useExisting: 'REDIS_CONNECTION',
        },

        // 其他服务
        RedisHealthService,
        RedisCacheService,
        RedisQueueService,
        RedisPubSubService,
        RedisLockService,
        RedisProtectionService,
        RedisBloomFilterService,
        RedisMonitoringService,
        CacheManagerService,
        CacheInterceptor,
        DataSourceFactory,
        CacheStrategyFactory,
      ],
      exports: [
        RedisService,
        'REDIS_SERVICE_CONTEXT',
        RedisHealthService,
        RedisCacheService,
        RedisQueueService,
        RedisPubSubService,
        RedisLockService,
        RedisProtectionService,
        RedisBloomFilterService,
        RedisMonitoringService,
        CacheManagerService,
        CacheInterceptor,
        DataSourceFactory,
        CacheStrategyFactory,
      ],
      global: true,
    };
  }

  /**
   * 同步模块 - 用于测试或特殊场景
   */
  static forRoot(): DynamicModule {
    return {
      module: RedisModule,
      imports: [ConfigModule],
      providers: [
        RedisService,
        RedisHealthService,
        RedisCacheService,
        RedisQueueService,
        RedisPubSubService,
        RedisLockService,
        RedisProtectionService,
        RedisBloomFilterService,
        RedisMonitoringService,
        CacheManagerService,
        CacheInterceptor,
        DataSourceFactory,
        CacheStrategyFactory,
      ],
      exports: [
        RedisService,
        RedisHealthService,
        RedisCacheService,
        RedisQueueService,
        RedisPubSubService,
        RedisLockService,
        RedisProtectionService,
        RedisBloomFilterService,
        RedisMonitoringService,
        CacheManagerService,
        CacheInterceptor,
        DataSourceFactory,
        CacheStrategyFactory,
      ],
      global: true,
    };
  }



  /**
   * 获取默认Redis配置
   */
  private static getDefaultConfig(configService: ConfigService) {
    return {
      host: configService.get('REDIS_HOST', 'localhost'),
      port: configService.get('REDIS_PORT', 6379),
      password: configService.get('REDIS_PASSWORD'),
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: false,
      keepAlive: 30000,
      family: 4,
      connectTimeout: 10000,
      commandTimeout: 5000,
    };
  }

  /**
   * 🔥 已废弃：根据服务分配数据库
   * 新架构使用Redis前缀隔离，所有服务使用同一个数据库（默认0）
   * 保留此方法仅为向后兼容，但始终返回0
   */
  private static getServiceDatabase(service: string): number {
    // 🔥 新架构：所有服务使用同一个Redis数据库（默认0）
    // 通过前缀隔离实现服务间数据隔离
    return 0;
  }

  /**
   * 构建基础前缀 - v3.0架构：只构建{环境}:{项目}:
   */
  private static buildServicePrefix(service: string, configService: ConfigService): string {
    const env = configService.get('NODE_ENV', 'dev');
    const project = configService.get('PROJECT_NAME', 'fm');

    // 验证配置
    this.validateConfiguration(env, project, service);

    // v3.0架构：RedisModule只构建基础前缀，dataType和service在RedisService中动态构建
    return `${env}:${project}:`;
  }

  /**
   * 验证配置有效性
   */
  private static validateConfiguration(env: string, project: string, service: string): void {
    // 验证环境
    const validEnvs = ['development', 'test', 'production'];
    if (!validEnvs.includes(env)) {
      throw new Error(`Invalid environment: ${env}. Must be one of: ${validEnvs.join(', ')}`);
    }

    // 验证项目名
    if (!project || project.length === 0) {
      throw new Error('Project name cannot be empty');
    }

    // 验证服务名
    if (!service || service.length === 0) {
      throw new Error('Service name cannot be empty');
    }

    // 使用导入的常量，避免硬编码
    const validServices = Object.values(MICROSERVICE_NAMES) as string[];

    if (!validServices.includes(service)) {
      console.warn(`Warning: Service '${service}' is not in the predefined list: ${validServices.join(', ')}`);
    }
  }
}
