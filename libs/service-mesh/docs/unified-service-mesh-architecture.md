# 统一ServiceMesh架构设计文档

## 🚀 **设计理念**

### **核心原则：一个模块，三种角色，零配置复杂度**

传统微服务架构中，不同类型的服务需要使用不同的配置模式，导致：
- 配置复杂度高
- 学习成本大
- 维护困难
- 容易出错

**统一ServiceMesh架构**通过智能推断和自动化配置，实现了：
- **一个API统一所有场景**
- **零配置智能工作**
- **需要时精确控制**
- **极致的开发体验**

## 🎯 **核心API设计**

### **统一注册方法**

```typescript
ServiceMeshModule.register(serviceName: string, options?: ServiceOptions)
```

### **ServiceOptions接口**

```typescript
interface ServiceOptions {
  role?: 'client' | 'global' | 'server';  // 服务角色，自动推断
  services?: string[];                     // 服务白名单，自动分析
  serverId?: string;                       // 区服ID，自动从环境变量获取
  weight?: number;                         // 负载均衡权重，默认1
  metadata?: Record<string, any>;          // 服务元数据，自动生成
  healthCheckPath?: string;                // 健康检查路径，默认/health
}
```

## 📋 **三种服务角色**

### **1. Client角色 - 纯客户端服务**
- **典型服务**：Gateway
- **特点**：只调用其他服务，不提供服务
- **自动推断条件**：serviceName === 'gateway'

```typescript
// 网关配置
ServiceMeshModule.register('gateway')

// 等价于：
ServiceMeshModule.register('gateway', {
  role: 'client',
  services: ['auth', 'character', 'hero', 'economy'] // 自动推断
})
```

### **2. Global角色 - 全局服务**
- **典型服务**：Auth, Payment, Notification
- **特点**：跨区服共享，全局唯一实例
- **自动推断条件**：serviceName in ['auth', 'payment', 'notification']

```typescript
// Auth服务配置
ServiceMeshModule.register('auth')

// 等价于：
ServiceMeshModule.register('auth', {
  role: 'global',
  services: [], // 通常无依赖
  metadata: {
    serviceType: 'global',
    features: ['authentication', 'authorization']
  }
})
```

### **3. Server角色 - 区服服务**
- **典型服务**：Character, Hero, Economy
- **特点**：区服隔离，既提供服务又调用其他服务
- **自动推断条件**：默认角色

```typescript
// Character服务配置
ServiceMeshModule.register('character')

// 等价于：
ServiceMeshModule.register('character', {
  role: 'server',
  serverId: process.env.SERVER_ID || 'server_001', // 自动获取
  services: ['hero', 'economy', 'activity'], // 自动推断依赖
  weight: 1,
  metadata: {
    version: '1.0.0',
    features: ['character-management', 'formation', 'inventory']
  }
})
```

## 🧠 **智能推断机制**

### **1. 服务角色自动推断**

```typescript
private static inferServiceRole(serviceName: string): ServiceRole {
  // 网关模式
  if (serviceName === 'gateway') return 'client';
  
  // 全局服务
  const globalServices = ['auth', 'payment', 'notification', 'analytics'];
  if (globalServices.includes(serviceName)) return 'global';
  
  // 默认区服服务
  return 'server';
}
```

### **2. 服务依赖自动分析**

```typescript
private static inferServiceDependencies(serviceName: string): string[] {
  const dependencyMap = {
    'gateway': ['auth', 'character', 'hero', 'economy', 'activity'],
    'character': ['hero', 'economy', 'activity'],
    'hero': ['character'],
    'economy': ['character'],
    'activity': ['character'],
    'auth': [], // 全局服务通常无依赖
    'payment': [],
    'notification': [],
  };
  
  return dependencyMap[serviceName] || [];
}
```

### **3. 环境配置自动获取**

```typescript
private static getAutoConfig(serviceName: string): AutoConfig {
  return {
    serverId: process.env.SERVER_ID || 'server_001',
    port: process.env[`${serviceName.toUpperCase()}_PORT`] || 3000,
    version: process.env.SERVICE_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  };
}
```

## 🎨 **使用示例**

### **零配置模式（推荐）**

```typescript
// apps/gateway/src/app.module.ts
@Module({
  imports: [
    ServiceMeshModule.register('gateway'), // 🚀 一行搞定！
  ],
})
export class AppModule {}

// apps/auth/src/app.module.ts
@Module({
  imports: [
    ServiceMeshModule.register('auth'), // 🚀 一行搞定！
  ],
})
export class AppModule {}

// apps/character/src/app.module.ts
@Module({
  imports: [
    ServiceMeshModule.register('character'), // 🚀 一行搞定！
  ],
})
export class AppModule {}
```

### **自定义配置模式**

```typescript
// 需要自定义时的优雅覆盖
ServiceMeshModule.register('character', {
  services: ['hero', 'economy'], // 覆盖默认依赖
  weight: 2,                     // 自定义权重
  metadata: {
    version: '2.0.0',
    features: ['advanced-formation'],
  },
})

// 强制指定角色
ServiceMeshModule.register('special-service', {
  role: 'global', // 强制设为全局服务
  services: ['auth', 'character'],
})
```

## 🔧 **内部实现架构**

### **统一注册方法实现**

```typescript
static register(serviceName: string, options?: ServiceOptions): DynamicModule {
  // 🚀 智能推断所有配置
  const role = options?.role ?? this.inferServiceRole(serviceName);
  const services = options?.services ?? this.inferServiceDependencies(serviceName);
  const serverId = options?.serverId ?? process.env.SERVER_ID ?? 'server_001';
  const autoConfig = this.getAutoConfig(serviceName);
  
  // 🎯 根据角色返回不同的模块配置
  switch (role) {
    case 'client':
      return this.createClientModule(serviceName, services, options);
    case 'global':
      return this.createGlobalModule(serviceName, autoConfig, options);
    case 'server':
      return this.createServerModule(serviceName, serverId, services, autoConfig, options);
    default:
      throw new Error(`Unknown service role: ${role}`);
  }
}
```

### **客户端模块创建**

```typescript
private static createClientModule(
  serviceName: string,
  services: string[],
  options?: ServiceOptions
): DynamicModule {
  return {
    module: ServiceMeshModule,
    global: true,
    imports: [
      ConfigModule,
      // 🚀 集成MicroserviceKit作为纯调用库
      MicroserviceKitModule.forClient({
        services,
        enableServerAware: true,
      }),
    ],
    providers: [
      UnifiedServiceDiscoveryService,
      GlobalServiceRegistryService,
      ServerAwareRegistryService,
      ServerAwareLoadBalancerService,
    ],
    exports: [
      MicroserviceClientService,
      UnifiedServiceDiscoveryService,
    ],
  };
}
```
