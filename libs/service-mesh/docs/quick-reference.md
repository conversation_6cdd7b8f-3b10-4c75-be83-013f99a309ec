# 统一ServiceMesh快速参考

## 🚀 **一分钟上手**

### **基本用法**
```typescript
// 🎯 统一API - 所有服务都用这一个方法
ServiceMeshModule.register(serviceName: string, options?: ServiceOptions)
```

### **零配置模式（推荐）**
```typescript
// 网关
ServiceMeshModule.register('gateway')

// Auth服务
ServiceMeshModule.register('auth')

// 业务服务
ServiceMeshModule.register('character')
ServiceMeshModule.register('hero')
ServiceMeshModule.register('economy')
```

## 📋 **服务角色自动推断**

| 服务名 | 自动角色 | 自动依赖 | 说明 |
|--------|----------|----------|------|
| `gateway` | `client` | `['auth', 'character', 'hero', 'economy']` | 纯客户端，不提供服务 |
| `auth` | `global` | `[]` | 全局服务，跨区服共享 |
| `payment` | `global` | `[]` | 全局服务，跨区服共享 |
| `notification` | `global` | `[]` | 全局服务，跨区服共享 |
| `character` | `server` | `['hero', 'economy', 'activity']` | 区服服务，既提供又调用 |
| `hero` | `server` | `['character']` | 区服服务，既提供又调用 |
| `economy` | `server` | `['character']` | 区服服务，既提供又调用 |
| `activity` | `server` | `['character']` | 区服服务，既提供又调用 |
| 其他 | `server` | `[]` | 默认区服服务 |

## 🔧 **自定义配置**

### **覆盖依赖关系**
```typescript
ServiceMeshModule.register('character', {
  services: ['hero', 'economy'], // 只调用这两个服务
})
```

### **强制指定角色**
```typescript
ServiceMeshModule.register('special-service', {
  role: 'global', // 强制设为全局服务
})
```

### **自定义元数据**
```typescript
ServiceMeshModule.register('character', {
  weight: 2,
  metadata: {
    version: '2.0.0',
    features: ['advanced-formation'],
  },
})
```

### **指定区服ID**
```typescript
ServiceMeshModule.register('character', {
  serverId: 'server_002', // 覆盖环境变量
})
```

## 🎨 **完整配置示例**

### **网关服务**
```typescript
// apps/gateway/src/app.module.ts
@Module({
  imports: [
    ServiceMeshModule.register('gateway'),
    // 其他模块...
  ],
})
export class AppModule {}
```

### **Auth服务**
```typescript
// apps/auth/src/app.module.ts
@Module({
  imports: [
    ServiceMeshModule.register('auth'),
    // 其他模块...
  ],
})
export class AppModule {}
```

### **Character服务**
```typescript
// apps/character/src/app.module.ts
@Module({
  imports: [
    ServiceMeshModule.register('character'),
    // 其他模块...
  ],
})
export class AppModule {}
```

### **Hero服务**
```typescript
// apps/hero/src/app.module.ts
@Module({
  imports: [
    ServiceMeshModule.register('hero'),
    // 其他模块...
  ],
})
export class AppModule {}
```

## 🌍 **环境变量**

### **必需的环境变量**
```bash
# .env
SERVER_ID=server_001              # 区服ID
NODE_ENV=development              # 环境
SERVICE_VERSION=1.0.0             # 服务版本
```

### **可选的环境变量**
```bash
# 服务特定端口
GATEWAY_PORT=3000
AUTH_PORT=3001
CHARACTER_PORT=3002
HERO_PORT=3003
ECONOMY_PORT=3004

# 其他配置
SERVICE_REGION=default
CLUSTER_NAME=main
```

## 🔍 **服务调用**

### **注入客户端服务**
```typescript
@Injectable()
export class CharacterService {
  constructor(
    private readonly microserviceClient: MicroserviceClientService,
  ) {}

  async getHeroInfo(heroId: string, serverId: string) {
    // 🚀 自动区服感知调用
    return this.microserviceClient.call('hero', 'hero.getInfo', {
      heroId,
      serverId, // 自动路由到对应区服
    });
  }

  async validateToken(token: string) {
    // 🌍 自动全局服务调用
    return this.microserviceClient.call('auth', 'auth.validate', {
      token, // Auth服务自动使用全局调用
    });
  }
}
```

### **服务白名单验证**
```typescript
@Injectable()
export class SomeService {
  constructor(
    @Inject('SERVICE_WHITELIST_VALIDATOR')
    private readonly whitelist: ServiceWhitelistValidator,
  ) {}

  async callService(serviceName: string) {
    // 🔒 自动白名单验证
    if (!this.whitelist.isServiceAllowed(serviceName)) {
      throw new Error(`Service ${serviceName} not allowed`);
    }
    
    // 继续调用...
  }
}
```

## 📊 **配置对比**

### **传统模式 vs 统一模式**

```typescript
// ❌ 传统模式：复杂配置
@Module({
  imports: [
    MicroserviceKitModule.forHybrid(
      MICROSERVICE_NAMES.CHARACTER_SERVICE,
      {
        services: [
          MICROSERVICE_NAMES.HERO_SERVICE,
          MICROSERVICE_NAMES.ECONOMY_SERVICE,
          MICROSERVICE_NAMES.ACTIVITY_SERVICE,
        ],
      }
    ),
    ServiceMeshModule.forServer('character', {
      serverId: process.env.SERVER_ID || 'server_001',
      weight: 1,
      metadata: {
        version: '1.0.0',
        features: ['character-management', 'formation'],
        description: '角色管理服务',
      },
    }),
  ],
})

// ✅ 统一模式：极简配置
@Module({
  imports: [
    ServiceMeshModule.register('character'), // 🚀 一行搞定！
  ],
})
```

## 🚨 **常见问题**

### **Q: 如何添加新的服务依赖？**
```typescript
// A: 手动指定services数组
ServiceMeshModule.register('character', {
  services: ['hero', 'economy', 'activity', 'new-service'],
})
```

### **Q: 如何禁用某个服务的自动注册？**
```typescript
// A: 设置autoRegister为false（需要在实现中支持）
ServiceMeshModule.register('character', {
  autoRegister: false,
})
```

### **Q: 如何调试服务发现问题？**
```typescript
// A: 启用调试日志
process.env.DEBUG = 'service-mesh:*'
```

### **Q: 如何处理服务名不在预定义列表中？**
```typescript
// A: 手动指定角色
ServiceMeshModule.register('custom-service', {
  role: 'server', // 或 'global' 或 'client'
  services: ['dependency1', 'dependency2'],
})
```

## 🎯 **最佳实践**

### **1. 命名规范**
- 使用清晰的服务名称：`character`, `hero`, `economy`
- 避免缩写：使用`authentication`而不是`auth`（除非是约定俗成的）
- 保持一致性：所有服务使用相同的命名风格

### **2. 依赖管理**
- 最小化依赖：只声明真正需要的服务
- 避免循环依赖：设计清晰的调用层次
- 定期审查：检查是否有不必要的依赖

### **3. 配置管理**
- 优先零配置：让自动推断处理大部分情况
- 最小化覆盖：只在必要时自定义配置
- 环境变量：使用环境变量管理环境特定配置

### **4. 监控和调试**
- 启用日志：在开发环境启用详细日志
- 监控调用：监控服务间调用的成功率和延迟
- 定期检查：定期检查服务注册状态

## 🔄 **迁移检查清单**

### **迁移前**
- [ ] 备份现有配置文件
- [ ] 确认环境变量设置
- [ ] 准备测试用例

### **迁移中**
- [ ] 逐个服务迁移
- [ ] 验证服务启动
- [ ] 测试服务间调用
- [ ] 检查服务注册状态

### **迁移后**
- [ ] 运行完整测试套件
- [ ] 清理旧配置代码
- [ ] 更新文档
- [ ] 团队培训

这个统一ServiceMesh架构让微服务配置变得**极其简单**！🚀
