#!/usr/bin/env ts-node

/**
 * 智能推断功能测试脚本
 * 
 * 只测试ServiceInferenceUtil的智能推断功能，避免模块依赖问题
 * 
 * 使用方法：
 * npx ts-node libs/service-mesh/scripts/test-inference-only.ts
 */

import { ServiceInferenceUtil } from '../src/utils/service-inference.util';

function testInferenceUtility() {
  console.log('🚀 开始测试智能推断功能...\n');

  // 测试1：服务角色推断
  console.log('📋 测试1：服务角色推断');
  console.log('='.repeat(50));
  
  const testServices = [
    'gateway', 'api-gateway', 'web-gateway',
    'auth', 'payment', 'notification', 'analytics',
    'character', 'hero', 'economy', 'activity', 'match', 'guild', 'social',
    'unknown-service'
  ];
  
  testServices.forEach(serviceName => {
    const role = ServiceInferenceUtil.inferServiceRole(serviceName);
    console.log(`🎯 ${serviceName.padEnd(15)} → ${role}`);
  });
  console.log('');

  // 测试2：依赖关系推断
  console.log('📋 测试2：依赖关系推断');
  console.log('='.repeat(50));
  
  testServices.forEach(serviceName => {
    const dependencies = ServiceInferenceUtil.inferServiceDependencies(serviceName);
    if (dependencies.length > 0) {
      console.log(`🔗 ${serviceName.padEnd(15)} → [${dependencies.join(', ')}]`);
    } else {
      console.log(`🔗 ${serviceName.padEnd(15)} → 无依赖`);
    }
  });
  console.log('');

  // 测试3：服务特性推断
  console.log('📋 测试3：服务特性推断');
  console.log('='.repeat(50));
  
  const importantServices = ['gateway', 'auth', 'character', 'hero', 'economy'];
  importantServices.forEach(serviceName => {
    const features = ServiceInferenceUtil.inferServiceFeatures(serviceName);
    console.log(`⚡ ${serviceName.padEnd(15)} → [${features.join(', ')}]`);
  });
  console.log('');

  // 测试4：环境配置自动获取
  console.log('📋 测试4：环境配置自动获取');
  console.log('='.repeat(50));
  
  const configServices = ['gateway', 'auth', 'character'];
  configServices.forEach(serviceName => {
    const autoConfig = ServiceInferenceUtil.getAutoConfig(serviceName);
    console.log(`🔧 ${serviceName}:`);
    console.log(`   区服ID: ${autoConfig.serverId}`);
    console.log(`   端口: ${autoConfig.port}`);
    console.log(`   版本: ${autoConfig.version}`);
    console.log(`   环境: ${autoConfig.environment}`);
    console.log(`   区域: ${autoConfig.region || 'default'}`);
    console.log(`   集群: ${autoConfig.cluster || 'main'}`);
    console.log('');
  });

  // 测试5：工具方法
  console.log('📋 测试5：工具方法');
  console.log('='.repeat(50));
  
  console.log('🌍 全局服务列表:');
  const globalServices = ServiceInferenceUtil.getGlobalServices();
  console.log(`   [${globalServices.join(', ')}]`);
  console.log('');
  
  console.log('💻 客户端服务列表:');
  const clientServices = ServiceInferenceUtil.getClientServices();
  console.log(`   [${clientServices.join(', ')}]`);
  console.log('');

  // 测试6：服务类型检查
  console.log('📋 测试6：服务类型检查');
  console.log('='.repeat(50));
  
  const checkServices = ['gateway', 'auth', 'character', 'unknown'];
  checkServices.forEach(serviceName => {
    const isGlobal = ServiceInferenceUtil.isGlobalService(serviceName);
    const isClient = ServiceInferenceUtil.isClientService(serviceName);
    console.log(`🔍 ${serviceName.padEnd(15)} → 全局: ${isGlobal ? '✅' : '❌'}, 客户端: ${isClient ? '✅' : '❌'}`);
  });
  console.log('');

  // 测试7：动态配置
  console.log('📋 测试7：动态配置测试');
  console.log('='.repeat(50));
  
  console.log('🔧 添加自定义全局服务...');
  ServiceInferenceUtil.addGlobalService('custom-global');
  console.log(`   custom-global 是否为全局服务: ${ServiceInferenceUtil.isGlobalService('custom-global') ? '✅' : '❌'}`);
  
  console.log('🔧 添加自定义客户端服务...');
  ServiceInferenceUtil.addClientService('custom-client');
  console.log(`   custom-client 是否为客户端服务: ${ServiceInferenceUtil.isClientService('custom-client') ? '✅' : '❌'}`);
  
  console.log('🔧 设置自定义依赖关系...');
  ServiceInferenceUtil.setServiceDependencies('custom-service', ['auth', 'character']);
  const customDeps = ServiceInferenceUtil.inferServiceDependencies('custom-service');
  console.log(`   custom-service 依赖: [${customDeps.join(', ')}]`);
  
  console.log('🔧 设置自定义服务特性...');
  ServiceInferenceUtil.setServiceFeatures('custom-service', ['feature1', 'feature2']);
  const customFeatures = ServiceInferenceUtil.inferServiceFeatures('custom-service');
  console.log(`   custom-service 特性: [${customFeatures.join(', ')}]`);
  console.log('');

  console.log('🎉 智能推断功能测试完成！');
  console.log('');
  console.log('📊 测试总结:');
  console.log('✅ 服务角色推断正常');
  console.log('✅ 依赖关系推断正常');
  console.log('✅ 服务特性推断正常');
  console.log('✅ 环境配置自动获取正常');
  console.log('✅ 工具方法正常');
  console.log('✅ 服务类型检查正常');
  console.log('✅ 动态配置功能正常');
  console.log('');
  console.log('🚀 ServiceInferenceUtil 智能推断功能完全正常！');
}

// 运行测试
if (require.main === module) {
  testInferenceUtility();
}

export { testInferenceUtility };
