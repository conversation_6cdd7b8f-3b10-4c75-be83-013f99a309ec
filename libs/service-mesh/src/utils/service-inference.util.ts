import { ServiceRole, ServiceDependencyMap, ServiceFeatureMap, AutoConfig } from '../interfaces/service-options.interface';

/**
 * 服务智能推断工具类
 * 
 * 提供服务角色、依赖关系、特性等的自动推断功能
 * 基于服务名称和业务逻辑进行智能分析
 */
export class ServiceInferenceUtil {

  /**
   * 全局服务列表
   * 这些服务跨区服共享，全局唯一实例
   */
  private static readonly GLOBAL_SERVICES = new Set([
    'auth',
    'payment', 
    'notification',
    'analytics',
    'monitoring',
    'logging',
  ]);

  /**
   * 客户端服务列表
   * 这些服务只调用其他服务，不提供服务
   */
  private static readonly CLIENT_SERVICES = new Set([
    'gateway',
    'api-gateway',
    'web-gateway',
  ]);

  /**
   * 服务依赖关系映射
   * 定义各服务的默认依赖关系
   */
  private static readonly DEPENDENCY_MAP: ServiceDependencyMap = {
    // 网关服务 - 需要调用所有业务服务
    'gateway': ['auth', 'character', 'hero', 'economy', 'activity', 'match', 'guild', 'social'],
    'api-gateway': ['auth', 'character', 'hero', 'economy', 'activity'],
    'web-gateway': ['auth', 'character', 'hero', 'economy'],

    // 核心业务服务
    'character': ['hero', 'economy', 'activity'],
    'hero': ['character'],
    'economy': ['character'],
    'activity': ['character'],
    'match': ['character', 'hero'],
    'guild': ['character', 'hero'],
    'social': ['character'],

    // 全局服务 - 通常无依赖或依赖其他全局服务
    'auth': ['notification'],
    'payment': ['auth'],
    'notification': ['auth'],
    'analytics': [],
    'monitoring': [],
    'logging': [],
  };

  /**
   * 服务特性映射
   * 定义各服务的默认特性列表
   */
  private static readonly FEATURE_MAP: ServiceFeatureMap = {
    // 网关服务
    'gateway': ['routing', 'load-balancing', 'authentication', 'rate-limiting'],
    'api-gateway': ['api-routing', 'request-validation', 'response-transformation'],
    'web-gateway': ['web-routing', 'session-management', 'static-serving'],

    // 核心业务服务
    'character': ['character-management', 'formation', 'inventory', 'tactic'],
    'hero': ['hero-management', 'cultivation', 'training', 'skills'],
    'economy': ['currency', 'shop', 'trading', 'auction'],
    'activity': ['events', 'quests', 'achievements', 'rewards'],
    'match': ['matchmaking', 'battle', 'tournament', 'ranking'],
    'guild': ['guild-management', 'alliance', 'war', 'cooperation'],
    'social': ['friends', 'chat', 'mail', 'community'],

    // 全局服务
    'auth': ['authentication', 'authorization', 'user-management', 'token-management'],
    'payment': ['payment-processing', 'billing', 'subscription', 'refund'],
    'notification': ['push-notification', 'email', 'sms', 'in-app-message'],
    'analytics': ['data-collection', 'reporting', 'metrics', 'insights'],
    'monitoring': ['health-check', 'performance-monitoring', 'alerting'],
    'logging': ['log-collection', 'log-analysis', 'audit-trail'],
  };

  /**
   * 推断服务角色
   * 基于服务名称自动推断服务角色
   * 
   * @param serviceName 服务名称
   * @returns 推断的服务角色
   */
  static inferServiceRole(serviceName: string): ServiceRole {
    // 1. 检查是否为客户端服务
    if (this.CLIENT_SERVICES.has(serviceName)) {
      return 'client';
    }

    // 2. 检查是否为全局服务
    if (this.GLOBAL_SERVICES.has(serviceName)) {
      return 'global';
    }

    // 3. 默认为区服服务
    return 'server';
  }

  /**
   * 推断服务依赖关系
   * 基于服务名称自动推断依赖的其他服务
   * 
   * @param serviceName 服务名称
   * @returns 依赖的服务列表
   */
  static inferServiceDependencies(serviceName: string): string[] {
    return this.DEPENDENCY_MAP[serviceName] || [];
  }

  /**
   * 推断服务特性
   * 基于服务名称自动推断服务特性
   * 
   * @param serviceName 服务名称
   * @returns 服务特性列表
   */
  static inferServiceFeatures(serviceName: string): string[] {
    return this.FEATURE_MAP[serviceName] || ['basic-service'];
  }

  /**
   * 获取自动配置
   * 从环境变量自动获取配置信息
   * 
   * @param serviceName 服务名称
   * @returns 自动配置对象
   */
  static getAutoConfig(serviceName: string): AutoConfig {
    return {
      serverId: process.env.SERVER_ID || 'server_001',
      port: this.getServicePort(serviceName),
      version: process.env.SERVICE_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      region: process.env.SERVICE_REGION || 'default',
      cluster: process.env.CLUSTER_NAME || 'main',
    };
  }

  /**
   * 获取服务端口
   * 基于服务名称和环境变量获取端口号
   * 
   * @param serviceName 服务名称
   * @returns 端口号
   */
  private static getServicePort(serviceName: string): number {
    // 1. 尝试从服务特定的环境变量获取
    const servicePortEnv = `${serviceName.toUpperCase()}_PORT`;
    const servicePort = process.env[servicePortEnv];
    if (servicePort) {
      return parseInt(servicePort, 10);
    }

    // 2. 尝试从通用端口环境变量获取
    const genericPort = process.env.PORT;
    if (genericPort) {
      return parseInt(genericPort, 10);
    }

    // 3. 基于服务名称返回默认端口
    const defaultPorts: Record<string, number> = {
      'gateway': 3000,
      'auth': 3001,
      'character': 3002,
      'hero': 3003,
      'economy': 3004,
      'activity': 3005,
      'match': 3006,
      'guild': 3007,
      'social': 3008,
      'payment': 3009,
      'notification': 3010,
      'analytics': 3011,
      'monitoring': 3012,
      'logging': 3013,
    };

    return defaultPorts[serviceName] || 3000;
  }

  /**
   * 检查是否为全局服务
   * 
   * @param serviceName 服务名称
   * @returns 是否为全局服务
   */
  static isGlobalService(serviceName: string): boolean {
    return this.GLOBAL_SERVICES.has(serviceName);
  }

  /**
   * 检查是否为客户端服务
   * 
   * @param serviceName 服务名称
   * @returns 是否为客户端服务
   */
  static isClientService(serviceName: string): boolean {
    return this.CLIENT_SERVICES.has(serviceName);
  }

  /**
   * 获取所有全局服务列表
   * 
   * @returns 全局服务列表
   */
  static getGlobalServices(): string[] {
    return Array.from(this.GLOBAL_SERVICES);
  }

  /**
   * 获取所有客户端服务列表
   * 
   * @returns 客户端服务列表
   */
  static getClientServices(): string[] {
    return Array.from(this.CLIENT_SERVICES);
  }

  /**
   * 添加自定义全局服务
   * 
   * @param serviceName 服务名称
   */
  static addGlobalService(serviceName: string): void {
    this.GLOBAL_SERVICES.add(serviceName);
  }

  /**
   * 添加自定义客户端服务
   * 
   * @param serviceName 服务名称
   */
  static addClientService(serviceName: string): void {
    this.CLIENT_SERVICES.add(serviceName);
  }

  /**
   * 设置自定义依赖关系
   * 
   * @param serviceName 服务名称
   * @param dependencies 依赖列表
   */
  static setServiceDependencies(serviceName: string, dependencies: string[]): void {
    this.DEPENDENCY_MAP[serviceName] = dependencies;
  }

  /**
   * 设置自定义服务特性
   * 
   * @param serviceName 服务名称
   * @param features 特性列表
   */
  static setServiceFeatures(serviceName: string, features: string[]): void {
    this.FEATURE_MAP[serviceName] = features;
  }
}
