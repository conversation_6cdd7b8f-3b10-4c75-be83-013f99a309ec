import { Module, Global, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RedisModule } from '@common/redis';

// 核心服务
import { ServerAwareRegistryService } from './registry/server-aware-registry.service';
import { ServerAwareLoadBalancerService } from './load-balancing/server-aware-load-balancer.service';

// 实例管理服务
import { InstanceLifecycleService } from './lifecycle/instance-lifecycle.service';
import { ServerConfigGeneratorService } from './orchestration/server-config-generator.service';
import { ContainerOrchestrationService } from './orchestration/container-orchestration.service';

// 自动注册服务
import { ServiceAutoRegistrationService } from './registry/service-auto-registration.service';

// 统一服务发现
import { UnifiedServiceDiscoveryService } from './discovery/unified-service-discovery.service';

// 全局服务注册
import { GlobalServiceRegistryService } from './registry/global-service-registry.service';
import { GlobalServiceAutoRegistrationService } from './registry/global-service-auto-registration.service';

/**
 * 服务网格模块 (Service Mesh Module)
 *
 * 提供完整的微服务基础设施，包括服务发现、注册、负载均衡和治理：
 * - 全局服务：跨区服共享，如Auth、Payment
 * - 区服服务：区服隔离，如Character、Hero
 * - 统一服务发现：智能识别服务类型
 * - 负载均衡：多种策略支持
 * - 实例管理：生命周期管理和健康检查
 *
 * 核心特性：
 * - 分层清晰：网关层、全局服务层、区服服务层
 * - 智能路由：自动选择合适的服务发现方式
 * - 负载均衡：全局和区服服务都支持负载均衡
 * - 零侵入性：业务代码最小化修改
 * - 容器编排：支持动态扩缩容和配置生成
 *
 * 使用模式（借鉴 microservice-kit 设计）：
 * - forRoot(): 网关模式，提供统一服务发现
 * - forGlobal(): 全局服务模式，如Auth
 * - forServer(): 区服服务模式，如Character
 */
@Module({})
export class ServiceMeshModule {

  /**
   * 网关模式 - 提供统一服务发现能力（用于Gateway）
   * 自动识别全局服务和区服服务，智能路由
   */
  static forRoot(): DynamicModule {
    return {
      module: ServiceMeshModule,
      global: true,
      imports: [
        ConfigModule,
        EventEmitterModule.forRoot(),
        // 🔧 修复：不重复注册RedisModule，复用主应用中的Redis服务
        // RedisModule已经在主应用中初始化，这里不需要重复创建
      ],
      providers: [
        // 统一服务发现（网关专用）
        UnifiedServiceDiscoveryService,

        // 全局服务注册中心
        GlobalServiceRegistryService,

        // 区服感知服务注册中心
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        InstanceLifecycleService,
        ServerConfigGeneratorService,
        ContainerOrchestrationService,
      ],
      exports: [
        UnifiedServiceDiscoveryService,  // 网关主要使用
        GlobalServiceRegistryService,
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        InstanceLifecycleService,
        ServerConfigGeneratorService,
        ContainerOrchestrationService,
      ],
    };
  }

  /**
   * 全局服务模式 - 用于Auth等跨区服服务
   * 特点：数据全局共享，支持全局负载均衡
   */
  static forGlobal(
    serviceName: string,
    options?: {
      weight?: number;               // 负载均衡权重，默认1
      metadata?: Record<string, any>; // 额外元数据
      healthCheckPath?: string;      // 健康检查路径，默认/health
    }
  ): DynamicModule {
    return {
      module: ServiceMeshModule,
      global: true,
      imports: [
        ConfigModule,
        // 🔧 修复：不创建新的Redis实例，复用现有的Redis服务
        // RedisModule已经在主应用中初始化，这里不需要重复创建
      ],
      providers: [
        GlobalServiceRegistryService,
        {
          provide: 'GLOBAL_SERVICE_CONFIG',
          useValue: {
            serviceName,
            weight: options?.weight ?? 1,
            metadata: options?.metadata ?? {},
            healthCheckPath: options?.healthCheckPath ?? '/health',
          },
        },
        GlobalServiceAutoRegistrationService,
      ],
      exports: [
        GlobalServiceRegistryService,
        GlobalServiceAutoRegistrationService,
      ],
    };
  }

  /**
   * 区服服务模式 - 用于Character等业务服务
   * 特点：数据区服隔离，支持区服内负载均衡
   */
  static forServer(
    serviceName: string,
    options?: {
      serverId?: string;             // 🎯 新增：显式指定区服ID，与RedisModule保持一致
      weight?: number;               // 负载均衡权重，默认1
      metadata?: Record<string, any>; // 额外元数据
      healthCheckPath?: string;      // 健康检查路径，默认/health
    }
  ): DynamicModule {
    return {
      module: ServiceMeshModule,
      global: true,
      imports: [
        ConfigModule,
        EventEmitterModule.forRoot(),
        // 🔧 修复：不重复注册RedisModule，复用主应用中的Redis服务
        // RedisModule已经在主应用中初始化，这里不需要重复创建
      ],
      providers: [
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        InstanceLifecycleService,
        {
          provide: 'SERVICE_REGISTRATION_CONFIG',
          useValue: {
            serviceName,
            serverId: options?.serverId, // 🎯 传递显式指定的serverId
            autoRegister: true, // 区服服务默认自动注册
            weight: options?.weight ?? 1,
            metadata: options?.metadata ?? {},
            healthCheckPath: options?.healthCheckPath ?? '/health',
          },
        },
        ServiceAutoRegistrationService,
      ],
      exports: [
        ServerAwareRegistryService,
        ServerAwareLoadBalancerService,
        InstanceLifecycleService,
        ServiceAutoRegistrationService,
      ],
    };
  }


}
