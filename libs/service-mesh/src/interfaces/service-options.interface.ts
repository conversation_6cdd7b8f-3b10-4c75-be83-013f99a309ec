/**
 * 统一ServiceMesh服务配置选项接口
 * 
 * 支持三种服务角色的统一配置：
 * - client: 纯客户端服务（如Gateway）
 * - global: 全局服务（如Auth、Payment）
 * - server: 区服服务（如Character、Hero）
 */
export interface ServiceOptions {
  /**
   * 服务角色，自动推断
   * - client: 只调用其他服务，不提供服务
   * - global: 跨区服共享，全局唯一实例
   * - server: 区服隔离，既提供服务又调用其他服务
   */
  role?: 'client' | 'global' | 'server';

  /**
   * 服务白名单，自动分析
   * 指定当前服务可以调用的其他服务列表
   */
  services?: string[];

  /**
   * 区服ID，自动从环境变量获取
   * 默认从 process.env.SERVER_ID 获取
   */
  serverId?: string;

  /**
   * 负载均衡权重，默认1
   * 用于负载均衡算法中的权重计算
   */
  weight?: number;

  /**
   * 服务元数据，自动生成
   * 包含版本、特性、描述等信息
   */
  metadata?: Record<string, any>;

  /**
   * 健康检查路径，默认/health
   * 用于服务健康状态检查
   */
  healthCheckPath?: string;

  /**
   * 是否启用自动注册，默认true
   * 控制服务是否自动注册到服务注册中心
   */
  autoRegister?: boolean;

  /**
   * 是否启用传统调用降级，默认false
   * 在新架构中通常禁用，保持纯区服感知调用
   */
  enableTraditionalFallback?: boolean;
}

/**
 * 服务角色类型
 */
export type ServiceRole = 'client' | 'global' | 'server';

/**
 * 自动配置接口
 * 从环境变量自动获取的配置信息
 */
export interface AutoConfig {
  /**
   * 区服ID
   */
  serverId: string;

  /**
   * 服务端口
   */
  port: number;

  /**
   * 服务版本
   */
  version: string;

  /**
   * 运行环境
   */
  environment: string;

  /**
   * 服务区域
   */
  region?: string;

  /**
   * 集群名称
   */
  cluster?: string;
}

/**
 * 服务依赖映射接口
 * 定义各服务的默认依赖关系
 */
export interface ServiceDependencyMap {
  [serviceName: string]: string[];
}

/**
 * 服务特性映射接口
 * 定义各服务的默认特性列表
 */
export interface ServiceFeatureMap {
  [serviceName: string]: string[];
}

/**
 * 服务白名单验证器接口
 */
export interface ServiceWhitelistValidator {
  /**
   * 检查服务是否在白名单中
   */
  isServiceAllowed(serviceName: string): boolean;

  /**
   * 获取允许的服务列表
   */
  getAllowedServices(): string[];

  /**
   * 添加允许的服务
   */
  addAllowedService?(service: string): void;

  /**
   * 移除允许的服务
   */
  removeAllowedService?(service: string): void;
}
