// 执行命令: mongosh --host *************** --port 27017 -u admin -p FslcxeE2025 --file scripts/create-microservice-users.js
// ==================== 足球经理游戏微服务数据库用户创建脚本 ====================

// 首先确保管理员用户存在
var adminDB = db.getSiblingDB('admin');

// 检查管理员用户是否已存在，若不存在则创建
try {
  if (!adminDB.getUser("admin")) {
    adminDB.createUser({
      user: "admin",
      pwd: "FslcxeE2025",
      roles: [
        { role: "root", db: "admin" }
      ]
    });
    print("✅ 管理员用户创建成功");
  } else {
    print("ℹ️  管理员用户已存在");
  }
} catch (e) {
  print("ℹ️  管理员用户可能已存在或无权限检查，尝试认证...");
}

// ==================== 角色服务数据库用户 ====================
var characterDB = db.getSiblingDB('character_db');
if (!characterDB.getUser("character-admin")) {
  characterDB.createUser({
    user: "character-admin",
    pwd: "FslcxeE2025",
    roles: [
      { role: "dbOwner", db: "character_db" }
    ]
  });
  print("✅ 角色服务数据库用户创建成功: character-admin");
} else {
  print("ℹ️  角色服务数据库用户已存在: character-admin");
}

// ==================== 球员服务数据库用户 ====================
var heroDB = db.getSiblingDB('hero_db');
if (!heroDB.getUser("hero-admin")) {
  heroDB.createUser({
    user: "hero-admin",
    pwd: "FslcxeE2025",
    roles: [
      { role: "dbOwner", db: "hero_db" }
    ]
  });
  print("✅ 球员服务数据库用户创建成功: hero-admin");
} else {
  print("ℹ️  球员服务数据库用户已存在: hero-admin");
}

// ==================== 经济服务数据库用户 ====================
var economyDB = db.getSiblingDB('economy_db');
if (!economyDB.getUser("economy-admin")) {
  economyDB.createUser({
    user: "economy-admin",
    pwd: "FslcxeE2025",
    roles: [
      { role: "dbOwner", db: "economy_db" }
    ]
  });
  print("✅ 经济服务数据库用户创建成功: economy-admin");
} else {
  print("ℹ️  经济服务数据库用户已存在: economy-admin");
}

// ==================== 社交服务数据库用户 ====================
var socialDB = db.getSiblingDB('social_db');
if (!socialDB.getUser("social-admin")) {
  socialDB.createUser({
    user: "social-admin",
    pwd: "FslcxeE2025",
    roles: [
      { role: "dbOwner", db: "social_db" }
    ]
  });
  print("✅ 社交服务数据库用户创建成功: social-admin");
} else {
  print("ℹ️  社交服务数据库用户已存在: social-admin");
}

// ==================== 比赛服务数据库用户 ====================
var matchDB = db.getSiblingDB('match_db');
if (!matchDB.getUser("match-admin")) {
  matchDB.createUser({
    user: "match-admin",
    pwd: "FslcxeE2025",
    roles: [
      { role: "dbOwner", db: "match_db" }
    ]
  });
  print("✅ 比赛服务数据库用户创建成功: match-admin");
} else {
  print("ℹ️  比赛服务数据库用户已存在: match-admin");
}

// ==================== 活动服务数据库用户 ====================
var activityDB = db.getSiblingDB('activity_db');
if (!activityDB.getUser("activity-admin")) {
  activityDB.createUser({
    user: "activity-admin",
    pwd: "FslcxeE2025",
    roles: [
      { role: "dbOwner", db: "activity_db" }
    ]
  });
  print("✅ 活动服务数据库用户创建成功: activity-admin");
} else {
  print("ℹ️  活动服务数据库用户已存在: activity-admin");
}

// ==================== 认证服务数据库用户 ====================
var authDB = db.getSiblingDB('auth_db');
if (!authDB.getUser("auth-admin")) {
  authDB.createUser({
    user: "auth-admin",
    pwd: "FslcxeE2025",
    roles: [
      { role: "dbOwner", db: "auth_db" }
    ]
  });
  print("✅ 认证服务数据库用户创建成功: auth-admin");
} else {
  print("ℹ️  认证服务数据库用户已存在: auth-admin");
}

// ==================== 旧项目数据库用户（用于数据迁移） ====================
var oldGameDB = db.getSiblingDB('old_game_db');
if (!oldGameDB.getUser("old-game-admin")) {
  oldGameDB.createUser({
    user: "old-game-admin",
    pwd: "FslcxeE2025",
    roles: [
      { role: "dbOwner", db: "old_game_db" }
    ]
  });
  print("✅ 旧项目数据库用户创建成功: old-game-admin");
} else {
  print("ℹ️  旧项目数据库用户已存在: old-game-admin");
}

print("\n==================== 用户创建完成 ====================");

// ==================== 验证所有用户 ====================
print("\n验证创建的用户:");

try {
  var characterUser = characterDB.getUser("character-admin");
  if (characterUser) {
    print("✅ character-admin 用户验证成功");
  }
} catch (e) {
  print("❌ character-admin 用户验证失败: " + e.message);
}

try {
  var heroUser = heroDB.getUser("hero-admin");
  if (heroUser) {
    print("✅ hero-admin 用户验证成功");
  }
} catch (e) {
  print("❌ hero-admin 用户验证失败: " + e.message);
}

try {
  var economyUser = economyDB.getUser("economy-admin");
  if (economyUser) {
    print("✅ economy-admin 用户验证成功");
  }
} catch (e) {
  print("❌ economy-admin 用户验证失败: " + e.message);
}

try {
  var socialUser = socialDB.getUser("social-admin");
  if (socialUser) {
    print("✅ social-admin 用户验证成功");
  }
} catch (e) {
  print("❌ social-admin 用户验证失败: " + e.message);
}

try {
  var matchUser = matchDB.getUser("match-admin");
  if (matchUser) {
    print("✅ match-admin 用户验证成功");
  }
} catch (e) {
  print("❌ match-admin 用户验证失败: " + e.message);
}

try {
  var activityUser = activityDB.getUser("activity-admin");
  if (activityUser) {
    print("✅ activity-admin 用户验证成功");
  }
} catch (e) {
  print("❌ activity-admin 用户验证失败: " + e.message);
}

try {
  var authUser = authDB.getUser("auth-admin");
  if (authUser) {
    print("✅ auth-admin 用户验证成功");
  }
} catch (e) {
  print("❌ auth-admin 用户验证失败: " + e.message);
}

try {
  var oldGameUser = oldGameDB.getUser("old-game-admin");
  if (oldGameUser) {
    print("✅ old-game-admin 用户验证成功");
  }
} catch (e) {
  print("❌ old-game-admin 用户验证失败: " + e.message);
}

print("\n🎉 所有微服务数据库用户创建和验证完成！");
print("\n数据库连接信息:");
print("角色服务: ************************************************************************");
print("球员服务: **************************************************************");
print("经济服务: ********************************************************************");
print("社交服务: ******************************************************************");
print("活动服务: **********************************************************************");
print("认证服务: **************************************************************");
print("比赛服务: ****************************************************************");
print("旧项目库: **********************************************************************");
